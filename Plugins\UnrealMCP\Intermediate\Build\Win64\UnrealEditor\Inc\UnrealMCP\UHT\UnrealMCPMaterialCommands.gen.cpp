// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Commands/UnrealMCPMaterialCommands.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeUnrealMCPMaterialCommands() {}

// ********** Begin Cross Module References ********************************************************
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
ENGINE_API UClass* Z_Construct_UClass_UMaterial_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialFunction_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInstanceConstant_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialParameterCollection_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UTexture_NoRegister();
UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPMaterialCommands();
UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPMaterialCommands_NoRegister();
UNREALMCP_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig();
UNREALMCP_API UScriptStruct* Z_Construct_UScriptStruct_FDynamicMaterialParams();
UPackage* Z_Construct_UPackage__Script_UnrealMCP();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FAuracronLayerMaterialConfig **************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronLayerMaterialConfig;
class UScriptStruct* FAuracronLayerMaterialConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLayerMaterialConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronLayerMaterialConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig, (UObject*)Z_Construct_UPackage__Script_UnrealMCP(), TEXT("AuracronLayerMaterialConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLayerMaterialConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Auracron layer material configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPMaterialCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron layer material configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaterialName_MetaData[] = {
		{ "Category", "AuracronLayerMaterialConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPMaterialCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerIndex_MetaData[] = {
		{ "Category", "AuracronLayerMaterialConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPMaterialCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PrimaryColor_MetaData[] = {
		{ "Category", "AuracronLayerMaterialConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPMaterialCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SecondaryColor_MetaData[] = {
		{ "Category", "AuracronLayerMaterialConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPMaterialCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AccentColor_MetaData[] = {
		{ "Category", "AuracronLayerMaterialConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPMaterialCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TertiaryColor_MetaData[] = {
		{ "Category", "AuracronLayerMaterialConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPMaterialCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Metallic_MetaData[] = {
		{ "Category", "AuracronLayerMaterialConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPMaterialCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Roughness_MetaData[] = {
		{ "Category", "AuracronLayerMaterialConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPMaterialCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Specular_MetaData[] = {
		{ "Category", "AuracronLayerMaterialConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPMaterialCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EmissiveStrength_MetaData[] = {
		{ "Category", "AuracronLayerMaterialConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPMaterialCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeatheringIntensity_MetaData[] = {
		{ "Category", "AuracronLayerMaterialConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPMaterialCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DetailNormalStrength_MetaData[] = {
		{ "Category", "AuracronLayerMaterialConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPMaterialCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SubsurfaceScattering_MetaData[] = {
		{ "Category", "AuracronLayerMaterialConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPMaterialCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Transparency_MetaData[] = {
		{ "Category", "AuracronLayerMaterialConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPMaterialCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MagicalGlowIntensity_MetaData[] = {
		{ "Category", "AuracronLayerMaterialConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPMaterialCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseNaniteOverride_MetaData[] = {
		{ "Category", "AuracronLayerMaterialConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPMaterialCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseMaterialLayers_MetaData[] = {
		{ "Category", "AuracronLayerMaterialConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPMaterialCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCreateMaterialInstance_MetaData[] = {
		{ "Category", "AuracronLayerMaterialConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPMaterialCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bGenerateProceduralTextures_MetaData[] = {
		{ "Category", "AuracronLayerMaterialConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPMaterialCommands.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_MaterialName;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LayerIndex;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PrimaryColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SecondaryColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AccentColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TertiaryColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Metallic;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Roughness;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Specular;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EmissiveStrength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WeatheringIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DetailNormalStrength;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SubsurfaceScattering;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Transparency;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MagicalGlowIntensity;
	static void NewProp_bUseNaniteOverride_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseNaniteOverride;
	static void NewProp_bUseMaterialLayers_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseMaterialLayers;
	static void NewProp_bCreateMaterialInstance_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCreateMaterialInstance;
	static void NewProp_bGenerateProceduralTextures_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bGenerateProceduralTextures;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronLayerMaterialConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_MaterialName = { "MaterialName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerMaterialConfig, MaterialName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaterialName_MetaData), NewProp_MaterialName_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_LayerIndex = { "LayerIndex", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerMaterialConfig, LayerIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerIndex_MetaData), NewProp_LayerIndex_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_PrimaryColor = { "PrimaryColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerMaterialConfig, PrimaryColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PrimaryColor_MetaData), NewProp_PrimaryColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_SecondaryColor = { "SecondaryColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerMaterialConfig, SecondaryColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SecondaryColor_MetaData), NewProp_SecondaryColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_AccentColor = { "AccentColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerMaterialConfig, AccentColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AccentColor_MetaData), NewProp_AccentColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_TertiaryColor = { "TertiaryColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerMaterialConfig, TertiaryColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TertiaryColor_MetaData), NewProp_TertiaryColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_Metallic = { "Metallic", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerMaterialConfig, Metallic), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Metallic_MetaData), NewProp_Metallic_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_Roughness = { "Roughness", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerMaterialConfig, Roughness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Roughness_MetaData), NewProp_Roughness_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_Specular = { "Specular", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerMaterialConfig, Specular), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Specular_MetaData), NewProp_Specular_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_EmissiveStrength = { "EmissiveStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerMaterialConfig, EmissiveStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EmissiveStrength_MetaData), NewProp_EmissiveStrength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_WeatheringIntensity = { "WeatheringIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerMaterialConfig, WeatheringIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeatheringIntensity_MetaData), NewProp_WeatheringIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_DetailNormalStrength = { "DetailNormalStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerMaterialConfig, DetailNormalStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DetailNormalStrength_MetaData), NewProp_DetailNormalStrength_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_SubsurfaceScattering = { "SubsurfaceScattering", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerMaterialConfig, SubsurfaceScattering), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SubsurfaceScattering_MetaData), NewProp_SubsurfaceScattering_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_Transparency = { "Transparency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerMaterialConfig, Transparency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Transparency_MetaData), NewProp_Transparency_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_MagicalGlowIntensity = { "MagicalGlowIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronLayerMaterialConfig, MagicalGlowIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MagicalGlowIntensity_MetaData), NewProp_MagicalGlowIntensity_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_bUseNaniteOverride_SetBit(void* Obj)
{
	((FAuracronLayerMaterialConfig*)Obj)->bUseNaniteOverride = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_bUseNaniteOverride = { "bUseNaniteOverride", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLayerMaterialConfig), &Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_bUseNaniteOverride_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseNaniteOverride_MetaData), NewProp_bUseNaniteOverride_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_bUseMaterialLayers_SetBit(void* Obj)
{
	((FAuracronLayerMaterialConfig*)Obj)->bUseMaterialLayers = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_bUseMaterialLayers = { "bUseMaterialLayers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLayerMaterialConfig), &Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_bUseMaterialLayers_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseMaterialLayers_MetaData), NewProp_bUseMaterialLayers_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_bCreateMaterialInstance_SetBit(void* Obj)
{
	((FAuracronLayerMaterialConfig*)Obj)->bCreateMaterialInstance = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_bCreateMaterialInstance = { "bCreateMaterialInstance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLayerMaterialConfig), &Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_bCreateMaterialInstance_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCreateMaterialInstance_MetaData), NewProp_bCreateMaterialInstance_MetaData) };
void Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_bGenerateProceduralTextures_SetBit(void* Obj)
{
	((FAuracronLayerMaterialConfig*)Obj)->bGenerateProceduralTextures = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_bGenerateProceduralTextures = { "bGenerateProceduralTextures", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronLayerMaterialConfig), &Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_bGenerateProceduralTextures_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bGenerateProceduralTextures_MetaData), NewProp_bGenerateProceduralTextures_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_MaterialName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_LayerIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_PrimaryColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_SecondaryColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_AccentColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_TertiaryColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_Metallic,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_Roughness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_Specular,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_EmissiveStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_WeatheringIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_DetailNormalStrength,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_SubsurfaceScattering,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_Transparency,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_MagicalGlowIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_bUseNaniteOverride,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_bUseMaterialLayers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_bCreateMaterialInstance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewProp_bGenerateProceduralTextures,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_UnrealMCP,
	nullptr,
	&NewStructOps,
	"AuracronLayerMaterialConfig",
	Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::PropPointers),
	sizeof(FAuracronLayerMaterialConfig),
	alignof(FAuracronLayerMaterialConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronLayerMaterialConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronLayerMaterialConfig.InnerSingleton, Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronLayerMaterialConfig.InnerSingleton;
}
// ********** End ScriptStruct FAuracronLayerMaterialConfig ****************************************

// ********** Begin ScriptStruct FDynamicMaterialParams ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FDynamicMaterialParams;
class UScriptStruct* FDynamicMaterialParams::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FDynamicMaterialParams.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FDynamicMaterialParams.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FDynamicMaterialParams, (UObject*)Z_Construct_UPackage__Script_UnrealMCP(), TEXT("DynamicMaterialParams"));
	}
	return Z_Registration_Info_UScriptStruct_FDynamicMaterialParams.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FDynamicMaterialParams_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Dynamic material parameters\n */" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPMaterialCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Dynamic material parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParameterName_MetaData[] = {
		{ "Category", "DynamicMaterialParams" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPMaterialCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScalarValue_MetaData[] = {
		{ "Category", "DynamicMaterialParams" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPMaterialCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VectorValue_MetaData[] = {
		{ "Category", "DynamicMaterialParams" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPMaterialCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TextureValue_MetaData[] = {
		{ "Category", "DynamicMaterialParams" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPMaterialCommands.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ParameterName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ScalarValue;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VectorValue;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_TextureValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FDynamicMaterialParams>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FDynamicMaterialParams_Statics::NewProp_ParameterName = { "ParameterName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDynamicMaterialParams, ParameterName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParameterName_MetaData), NewProp_ParameterName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FDynamicMaterialParams_Statics::NewProp_ScalarValue = { "ScalarValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDynamicMaterialParams, ScalarValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScalarValue_MetaData), NewProp_ScalarValue_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FDynamicMaterialParams_Statics::NewProp_VectorValue = { "VectorValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDynamicMaterialParams, VectorValue), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VectorValue_MetaData), NewProp_VectorValue_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FDynamicMaterialParams_Statics::NewProp_TextureValue = { "TextureValue", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDynamicMaterialParams, TextureValue), Z_Construct_UClass_UTexture_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TextureValue_MetaData), NewProp_TextureValue_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FDynamicMaterialParams_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDynamicMaterialParams_Statics::NewProp_ParameterName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDynamicMaterialParams_Statics::NewProp_ScalarValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDynamicMaterialParams_Statics::NewProp_VectorValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDynamicMaterialParams_Statics::NewProp_TextureValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FDynamicMaterialParams_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FDynamicMaterialParams_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_UnrealMCP,
	nullptr,
	&NewStructOps,
	"DynamicMaterialParams",
	Z_Construct_UScriptStruct_FDynamicMaterialParams_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FDynamicMaterialParams_Statics::PropPointers),
	sizeof(FDynamicMaterialParams),
	alignof(FDynamicMaterialParams),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FDynamicMaterialParams_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FDynamicMaterialParams_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FDynamicMaterialParams()
{
	if (!Z_Registration_Info_UScriptStruct_FDynamicMaterialParams.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FDynamicMaterialParams.InnerSingleton, Z_Construct_UScriptStruct_FDynamicMaterialParams_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FDynamicMaterialParams.InnerSingleton;
}
// ********** End ScriptStruct FDynamicMaterialParams **********************************************

// ********** Begin Class UUnrealMCPMaterialCommands ***********************************************
void UUnrealMCPMaterialCommands::StaticRegisterNativesUUnrealMCPMaterialCommands()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UUnrealMCPMaterialCommands;
UClass* UUnrealMCPMaterialCommands::GetPrivateStaticClass()
{
	using TClass = UUnrealMCPMaterialCommands;
	if (!Z_Registration_Info_UClass_UUnrealMCPMaterialCommands.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("UnrealMCPMaterialCommands"),
			Z_Registration_Info_UClass_UUnrealMCPMaterialCommands.InnerSingleton,
			StaticRegisterNativesUUnrealMCPMaterialCommands,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UUnrealMCPMaterialCommands.InnerSingleton;
}
UClass* Z_Construct_UClass_UUnrealMCPMaterialCommands_NoRegister()
{
	return UUnrealMCPMaterialCommands::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * UnrealMCP Material Commands - Modern UE 5.6.1 Implementation\n * Handles procedural material creation with Auracron-specific themes\n */" },
#endif
		{ "IncludePath", "Commands/UnrealMCPMaterialCommands.h" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPMaterialCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "UnrealMCP Material Commands - Modern UE 5.6.1 Implementation\nHandles procedural material creation with Auracron-specific themes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreatedMaterials_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache for created materials\n" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPMaterialCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache for created materials" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreatedMaterialInstances_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache for material instances\n" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPMaterialCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache for material instances" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreatedMaterialFunctions_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache for material functions\n" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPMaterialCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache for material functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreatedParameterCollections_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache for parameter collections\n" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPMaterialCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache for parameter collections" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreatedTextures_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache for textures\n" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPMaterialCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache for textures" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CreatedMaterials_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CreatedMaterials_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CreatedMaterials;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CreatedMaterialInstances_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CreatedMaterialInstances_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CreatedMaterialInstances;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CreatedMaterialFunctions_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CreatedMaterialFunctions_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CreatedMaterialFunctions;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CreatedParameterCollections_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CreatedParameterCollections_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CreatedParameterCollections;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CreatedTextures_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CreatedTextures_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CreatedTextures;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UUnrealMCPMaterialCommands>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::NewProp_CreatedMaterials_ValueProp = { "CreatedMaterials", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UMaterial_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::NewProp_CreatedMaterials_Key_KeyProp = { "CreatedMaterials_Key", nullptr, (EPropertyFlags)0x0100000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::NewProp_CreatedMaterials = { "CreatedMaterials", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UUnrealMCPMaterialCommands, CreatedMaterials), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreatedMaterials_MetaData), NewProp_CreatedMaterials_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::NewProp_CreatedMaterialInstances_ValueProp = { "CreatedMaterialInstances", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UMaterialInstanceConstant_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::NewProp_CreatedMaterialInstances_Key_KeyProp = { "CreatedMaterialInstances_Key", nullptr, (EPropertyFlags)0x0100000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::NewProp_CreatedMaterialInstances = { "CreatedMaterialInstances", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UUnrealMCPMaterialCommands, CreatedMaterialInstances), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreatedMaterialInstances_MetaData), NewProp_CreatedMaterialInstances_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::NewProp_CreatedMaterialFunctions_ValueProp = { "CreatedMaterialFunctions", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UMaterialFunction_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::NewProp_CreatedMaterialFunctions_Key_KeyProp = { "CreatedMaterialFunctions_Key", nullptr, (EPropertyFlags)0x0100000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::NewProp_CreatedMaterialFunctions = { "CreatedMaterialFunctions", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UUnrealMCPMaterialCommands, CreatedMaterialFunctions), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreatedMaterialFunctions_MetaData), NewProp_CreatedMaterialFunctions_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::NewProp_CreatedParameterCollections_ValueProp = { "CreatedParameterCollections", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UMaterialParameterCollection_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::NewProp_CreatedParameterCollections_Key_KeyProp = { "CreatedParameterCollections_Key", nullptr, (EPropertyFlags)0x0100000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::NewProp_CreatedParameterCollections = { "CreatedParameterCollections", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UUnrealMCPMaterialCommands, CreatedParameterCollections), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreatedParameterCollections_MetaData), NewProp_CreatedParameterCollections_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::NewProp_CreatedTextures_ValueProp = { "CreatedTextures", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::NewProp_CreatedTextures_Key_KeyProp = { "CreatedTextures_Key", nullptr, (EPropertyFlags)0x0100000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::NewProp_CreatedTextures = { "CreatedTextures", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UUnrealMCPMaterialCommands, CreatedTextures), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreatedTextures_MetaData), NewProp_CreatedTextures_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::NewProp_CreatedMaterials_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::NewProp_CreatedMaterials_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::NewProp_CreatedMaterials,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::NewProp_CreatedMaterialInstances_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::NewProp_CreatedMaterialInstances_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::NewProp_CreatedMaterialInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::NewProp_CreatedMaterialFunctions_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::NewProp_CreatedMaterialFunctions_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::NewProp_CreatedMaterialFunctions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::NewProp_CreatedParameterCollections_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::NewProp_CreatedParameterCollections_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::NewProp_CreatedParameterCollections,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::NewProp_CreatedTextures_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::NewProp_CreatedTextures_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::NewProp_CreatedTextures,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_UnrealMCP,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::ClassParams = {
	&UUnrealMCPMaterialCommands::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::Class_MetaDataParams), Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UUnrealMCPMaterialCommands()
{
	if (!Z_Registration_Info_UClass_UUnrealMCPMaterialCommands.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UUnrealMCPMaterialCommands.OuterSingleton, Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UUnrealMCPMaterialCommands.OuterSingleton;
}
UUnrealMCPMaterialCommands::UUnrealMCPMaterialCommands(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UUnrealMCPMaterialCommands);
UUnrealMCPMaterialCommands::~UUnrealMCPMaterialCommands() {}
// ********** End Class UUnrealMCPMaterialCommands *************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPMaterialCommands_h__Script_UnrealMCP_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronLayerMaterialConfig::StaticStruct, Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics::NewStructOps, TEXT("AuracronLayerMaterialConfig"), &Z_Registration_Info_UScriptStruct_FAuracronLayerMaterialConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronLayerMaterialConfig), 3630941609U) },
		{ FDynamicMaterialParams::StaticStruct, Z_Construct_UScriptStruct_FDynamicMaterialParams_Statics::NewStructOps, TEXT("DynamicMaterialParams"), &Z_Registration_Info_UScriptStruct_FDynamicMaterialParams, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FDynamicMaterialParams), 2814276171U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UUnrealMCPMaterialCommands, UUnrealMCPMaterialCommands::StaticClass, TEXT("UUnrealMCPMaterialCommands"), &Z_Registration_Info_UClass_UUnrealMCPMaterialCommands, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UUnrealMCPMaterialCommands), 2512247192U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPMaterialCommands_h__Script_UnrealMCP_954955339(TEXT("/Script/UnrealMCP"),
	Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPMaterialCommands_h__Script_UnrealMCP_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPMaterialCommands_h__Script_UnrealMCP_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPMaterialCommands_h__Script_UnrealMCP_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPMaterialCommands_h__Script_UnrealMCP_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
