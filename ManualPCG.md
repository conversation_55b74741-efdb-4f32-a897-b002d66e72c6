Manual de Implementação PCG Framework – Unreal Engine 5.6
1. Estrutura Básica de um PCG Graph

Um gráfico PCG ainda segue o fluxo principal:

[Input Data] → [Point Generation] → [Filters/Processors] → [Spawner/Output]


Mas agora temos novos nós, APIs e otimizações, então o design de nível pode ser muito mais eficiente e escalável.

2. Inputs Modernos

PCG Volume / Spline / Landscape continuam sendo as principais fontes.

Novidade 5.6:

Get Landscape Node pode acessar Runtime Virtual Textures (RVTs) diretamente.

Entrada de dados pode vir de Biome Core V2 (definindo zonas de bioma, densidade, blend rules).

3. Geração de Pontos

Métodos clássicos: Grid, Scatter, Noise.

5.6 otimização:

Uso de Structure of Arrays (SoA) para atributos dos pontos.

Pré-processamento (Prepare Step) para octrees, cache de landscape etc.

📌 Exemplo: usar Noise-based Scatter limitado por uma RVT que contém informações de altitude/vegetação.

4. Processamento & Filtros

Filtros clássicos: inclinação, altitude, colisão.

5.6 APIs novas:

Get Virtual Texture Data (GPU): ler informações direto do RVT (ex: um mapa de altura pintado no landscape).

Get Static Mesh Resource Data (GPU): acessar bounding box, LODs, vértices de meshes em runtime para alinhar pontos.

Generate Grass Maps: gerar mapas de distribuição baseados em terreno.

5. Spawning & Output

Continua suportando:

Instanced Static Meshes (HISM)

Blueprint Actors

5.6 melhoria:

Frustum culling integrado (apenas gera o que o jogador pode ver).

Melhor integração com streaming de mundo (carregamento dinâmico).

6. Performance & Runtime Control
Configurações novas:

pcg.RuntimeGeneration.NumGeneratingComponents → controla quantos componentes podem gerar simultaneamente.

pcg.RuntimeGeneration.BudgetPerFrame → define tempo por frame para geração.

Cache inteligente com CRC por Data → evita reprocessamento desnecessário.

📌 Recomendação para jogos grandes (ex: MOBA ou open-world):

Ativar multithreading no PCG.

Usar streaming + culling para só gerar objetos próximos.

7. Biome Core V2 (Experimental, mas poderoso 🚀)

O Biome Core V2 permite organizar geração procedural em zonas de biomas:

Definir múltiplos biomas (floresta, pântano, deserto).

Blending entre biomas (ex: fade gradual floresta → campo aberto).

Cache local por biome actor (otimiza carregamento).

Permite subgráficos reutilizáveis para modularidade.

📌 Exemplo:

Criar um Bioma Floresta que usa scatter GPU para árvores pequenas + mapas RVT para distribuir grama.

Criar um Bioma Pântano com densidade controlada por ruído + spawn de meshes com rotação randomizada.

Conectar ambos com regras de blend de borda.

8. Debug & Workflow Moderno

Painel de Parâmetros (dockable): arrastar e soltar parâmetros no gráfico.

Data Viewports múltiplos: inspecionar pontos, meshes, volumes e colisões diretamente no editor.

Debug persistente: seleção de atributos e filtros mais claros.

9. Exemplo Prático (Pseudo-Setup)

Objetivo: Espalhar árvores em um landscape, mas só em áreas planas e controladas por um RVT.

Input: Get Landscape Node → Landscape + RVT.

Point Generation: Scatter Points (Noise) dentro do PCG Volume.

Processor:

Get Virtual Texture Data (GPU) → lê inclinação do terreno.

Filtro: só mantém pontos com inclinação < 15°.

Spawner: Static Mesh Spawner → instanciar árvores (HISM).

Optimization: ativar frustum culling + budget runtime.

✅ Resumo

O fluxo clássico ainda é válido, mas no UE 5.6 você deve usar GPU APIs, otimizações de caching/multithread e Biome Core V2.

O resultado é um design de nível procedural mais rápido, modular, escalável e realista.

Isso permite jogos como o seu MOBA 5x5 com múltiplos layers serem totalmente automáticos, sem intervenção manual.