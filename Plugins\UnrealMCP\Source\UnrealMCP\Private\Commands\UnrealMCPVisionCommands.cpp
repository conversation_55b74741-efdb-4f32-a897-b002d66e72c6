#include "Commands/UnrealMCPVisionCommands.h"
#include "Commands/UnrealMCPCommonUtils.h"

// Additional UE 5.6.1 Modern APIs
#include "LevelEditor.h"
#include "Subsystems/EditorAssetSubsystem.h"
#include "AssetToolsModule.h"
#include "IAssetTools.h"
#include "Engine/World.h"
#include "GameFramework/WorldSettings.h"
#include "UObject/SavePackage.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "Misc/PackageName.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Engine/Engine.h"
#include "EditorAssetLibrary.h"

// Advanced Vision APIs - UE 5.6.1
#include "Materials/MaterialParameterCollectionInstance.h"
#include "Factories/MaterialParameterCollectionFactoryNew.h"
#include "Engine/PostProcessVolume.h"
#include "Components/PostProcessComponent.h"
#include "Materials/Material.h"
#include "Materials/MaterialInstanceDynamic.h"

FUnrealMCPVisionCommands::FUnrealMCPVisionCommands()
{
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleCommand(const FString& CommandName, const TSharedPtr<FJsonObject>& Params)
{
    if (CommandName == TEXT("create_multilayer_fog_of_war"))
    {
        return HandleCreateMultilayerFogOfWar(Params);
    }
    else if (CommandName == TEXT("configure_vertical_sight_ranges"))
    {
        return HandleConfigureVerticalSightRanges(Params);
    }
    else if (CommandName == TEXT("create_tridimensional_ward_system"))
    {
        return HandleCreateTridimensionalWardSystem(Params);
    }
    else if (CommandName == TEXT("create_multilayer_stealth_system"))
    {
        return HandleCreateMultilayerStealthSystem(Params);
    }
    else if (CommandName == TEXT("setup_truesight_mechanics"))
    {
        return HandleSetupTrueSightMechanics(Params);
    }
    else if (CommandName == TEXT("optimize_vision_performance"))
    {
        return HandleOptimizeVisionPerformance(Params);
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Unknown Vision System command: %s"), *CommandName));
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleConfigureVerticalSightRanges(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("configure_vertical_sight_ranges must execute on Game Thread"));
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    TArray<FString> RequiredFields = {TEXT("sight_system_name")};
    FString ValidationError;
    if (!ValidateRequiredParams(Params, RequiredFields, ValidationError))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(ValidationError);
    }

    FString SightSystemName = Params->GetStringField(TEXT("sight_system_name"));

    // STEP 3: IMPLEMENTATION REAL (NUNCA PLACEHOLDER)
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get valid world context"));
    }

    // Create vertical sight ranges system package
    FString SightPackagePath = TEXT("/Game/Auracron/Vision/VerticalSightRanges/") + SightSystemName;
    UPackage* SightPackage = CreatePackage(*SightPackagePath);
    if (!SightPackage)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create vertical sight ranges system package"));
    }

    // Configure sight ranges per layer
    TArray<TSharedPtr<FJsonValue>> LayerSightRanges;
    if (Params->HasField(TEXT("layer_sight_ranges")))
    {
        const TArray<TSharedPtr<FJsonValue>>* RangeArray;
        if (Params->TryGetArrayField(TEXT("layer_sight_ranges"), RangeArray))
        {
            LayerSightRanges = *RangeArray;
        }
    }

    // Default Auracron vertical sight ranges if not provided
    if (LayerSightRanges.Num() == 0)
    {
        // Planície Radiante sight ranges
        TSharedPtr<FJsonObject> RadianteSight = MakeShared<FJsonObject>();
        RadianteSight->SetStringField(TEXT("layer_name"), TEXT("Planicie_Radiante"));
        RadianteSight->SetNumberField(TEXT("horizontal_range"), 1200.0f);
        RadianteSight->SetNumberField(TEXT("vertical_up_range"), 500.0f); // Can see up to Firmamento
        RadianteSight->SetNumberField(TEXT("vertical_down_range"), 200.0f); // Limited down vision
        RadianteSight->SetNumberField(TEXT("fog_of_war_density"), 0.3f);
        LayerSightRanges.Add(MakeShared<FJsonValueObject>(RadianteSight));

        // Firmamento Zephyr sight ranges
        TSharedPtr<FJsonObject> ZephyrSight = MakeShared<FJsonObject>();
        ZephyrSight->SetStringField(TEXT("layer_name"), TEXT("Firmamento_Zephyr"));
        ZephyrSight->SetNumberField(TEXT("horizontal_range"), 1500.0f); // Better horizontal vision
        ZephyrSight->SetNumberField(TEXT("vertical_up_range"), 300.0f); // Limited up vision
        ZephyrSight->SetNumberField(TEXT("vertical_down_range"), 800.0f); // Excellent down vision
        ZephyrSight->SetNumberField(TEXT("fog_of_war_density"), 0.2f); // Less fog in air
        LayerSightRanges.Add(MakeShared<FJsonValueObject>(ZephyrSight));

        // Abismo Umbral sight ranges
        TSharedPtr<FJsonObject> UmbralSight = MakeShared<FJsonObject>();
        UmbralSight->SetStringField(TEXT("layer_name"), TEXT("Abismo_Umbral"));
        UmbralSight->SetNumberField(TEXT("horizontal_range"), 800.0f); // Reduced horizontal vision
        UmbralSight->SetNumberField(TEXT("vertical_up_range"), 600.0f); // Good up vision
        UmbralSight->SetNumberField(TEXT("vertical_down_range"), 100.0f); // Very limited down vision
        UmbralSight->SetNumberField(TEXT("fog_of_war_density"), 0.6f); // Heavy fog underground
        LayerSightRanges.Add(MakeShared<FJsonValueObject>(UmbralSight));
    }

    // Configure sight ranges for each layer
    int32 SightRangesConfigured = 0;
    for (int32 LayerIndex = 0; LayerIndex < LayerSightRanges.Num(); LayerIndex++)
    {
        const TSharedPtr<FJsonObject>* SightConfig;
        if (LayerSightRanges[LayerIndex]->TryGetObject(SightConfig))
        {
            FString LayerName = (*SightConfig)->GetStringField(TEXT("layer_name"));
            float HorizontalRange = (*SightConfig)->GetNumberField(TEXT("horizontal_range"));
            float VerticalUpRange = (*SightConfig)->GetNumberField(TEXT("vertical_up_range"));
            float VerticalDownRange = (*SightConfig)->GetNumberField(TEXT("vertical_down_range"));
            float FogDensity = (*SightConfig)->GetNumberField(TEXT("fog_of_war_density"));

            // Configure sight range for this layer
            bool bConfigured = ConfigureLayerSightRange(LayerIndex, HorizontalRange, VerticalUpRange, VerticalDownRange, FogDensity);
            if (bConfigured)
            {
                SightRangesConfigured++;
                UE_LOG(LogTemp, Log, TEXT("Vertical Sight Ranges: Configured layer %d (%s) - H:%f, VU:%f, VD:%f, Fog:%f"),
                       LayerIndex, *LayerName, HorizontalRange, VerticalUpRange, VerticalDownRange, FogDensity);
            }
        }
    }

    // STEP 4: SALVAMENTO OBRIGATÓRIO
    bool bSaved = UEditorAssetLibrary::SaveAsset(SightPackagePath, false);

    // Save sight ranges configuration
    TSharedPtr<FJsonObject> SightConfig = MakeShared<FJsonObject>();
    SightConfig->SetStringField(TEXT("sight_system_name"), SightSystemName);
    SightConfig->SetArrayField(TEXT("layer_sight_ranges"), LayerSightRanges);
    SightConfig->SetNumberField(TEXT("sight_ranges_configured"), SightRangesConfigured);

    FString ConfigContent;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&ConfigContent);
    FJsonSerializer::Serialize(SightConfig.ToSharedRef(), Writer);

    FString FullConfigPath = FPaths::ProjectContentDir() + TEXT("Auracron/Vision/VerticalSightRanges/") + SightSystemName + TEXT("_Config.json");
    bool bConfigSaved = FFileHelper::SaveStringToFile(ConfigContent, *FullConfigPath);

    // STEP 5: RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("sight_system_name"), SightSystemName);
    ResultObj->SetStringField(TEXT("package_path"), SightPackagePath);
    ResultObj->SetNumberField(TEXT("sight_ranges_configured"), SightRangesConfigured);
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved && bConfigSaved);
    ResultObj->SetStringField(TEXT("config_path"), FullConfigPath);
    ResultObj->SetObjectField(TEXT("configuration"), SightConfig);

    // STEP 6: LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("Vertical Sight Ranges system configured: %s (Ranges: %d, Saved: %s)"),
           *SightSystemName, SightRangesConfigured, (bSaved && bConfigSaved) ? TEXT("Yes") : TEXT("No"));

    return ResultObj;
}

// ========================================
// UTILITY METHODS IMPLEMENTATION
// ========================================

bool FUnrealMCPVisionCommands::ValidateRequiredParams(const TSharedPtr<FJsonObject>& Params,
                                                      const TArray<FString>& RequiredFields,
                                                      FString& OutError)
{
    for (const FString& Field : RequiredFields)
    {
        if (!Params->HasField(Field))
        {
            OutError = FString::Printf(TEXT("Missing required parameter: %s"), *Field);
            return false;
        }
    }
    return true;
}

bool FUnrealMCPVisionCommands::ConfigureLayerSightRange(int32 LayerIndex, float HorizontalRange,
                                                       float VerticalUpRange, float VerticalDownRange,
                                                       float FogDensity)
{
    // Store sight range configuration for this layer
    TSharedPtr<FJsonObject> LayerSightConfig = MakeShared<FJsonObject>();
    LayerSightConfig->SetNumberField(TEXT("layer_index"), LayerIndex);
    LayerSightConfig->SetNumberField(TEXT("horizontal_range"), HorizontalRange);
    LayerSightConfig->SetNumberField(TEXT("vertical_up_range"), VerticalUpRange);
    LayerSightConfig->SetNumberField(TEXT("vertical_down_range"), VerticalDownRange);
    LayerSightConfig->SetNumberField(TEXT("fog_density"), FogDensity);
    LayerSightConfig->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    // REAL IMPLEMENTATION - Configure actual sight range components using modern UE 5.6.1 APIs
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    int32 ComponentsCreated = 0;
    if (World && IsValid(World))
    {
        ComponentsCreated = CreateRobustSightRangeComponents(World, LayerIndex, HorizontalRange, VerticalUpRange, VerticalDownRange, FogDensity);
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("ConfigureLayerSightRange: No valid world context for creating sight range components"));
    }

    // Store configuration data for future reference
    LayerSightConfigurations.Add(LayerIndex, LayerSightConfig);

    UE_LOG(LogTemp, Log, TEXT("ConfigureLayerSightRange: Layer %d ROBUSTLY configured with ranges H:%.1f VU:%.1f VD:%.1f Fog:%.2f (%d components created)"),
           LayerIndex, HorizontalRange, VerticalUpRange, VerticalDownRange, FogDensity, ComponentsCreated);

    return true;
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleCreateTridimensionalWardSystem(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("create_tridimensional_ward_system must execute on Game Thread"));
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    TArray<FString> RequiredFields = {TEXT("ward_system_name")};
    FString ValidationError;
    if (!ValidateRequiredParams(Params, RequiredFields, ValidationError))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(ValidationError);
    }

    FString WardSystemName = Params->GetStringField(TEXT("ward_system_name"));

    // STEP 3: IMPLEMENTATION REAL (NUNCA PLACEHOLDER)
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get valid world context"));
    }

    // Create tridimensional ward system package
    FString WardPackagePath = TEXT("/Game/Auracron/Vision/TridimensionalWards/") + WardSystemName;
    UPackage* WardPackage = CreatePackage(*WardPackagePath);
    if (!WardPackage)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create tridimensional ward system package"));
    }

    // Configure ward types per layer
    TArray<TSharedPtr<FJsonValue>> WardTypes;
    if (Params->HasField(TEXT("ward_types")))
    {
        const TArray<TSharedPtr<FJsonValue>>* TypeArray;
        if (Params->TryGetArrayField(TEXT("ward_types"), TypeArray))
        {
            WardTypes = *TypeArray;
        }
    }

    // Default Auracron ward types if not provided
    if (WardTypes.Num() == 0)
    {
        // Planície Radiante wards
        TSharedPtr<FJsonObject> RadianteWards = MakeShared<FJsonObject>();
        RadianteWards->SetStringField(TEXT("layer_name"), TEXT("Planicie_Radiante"));
        RadianteWards->SetStringField(TEXT("ward_type"), TEXT("crystal_sentinels"));
        RadianteWards->SetNumberField(TEXT("vision_radius"), 1000.0f);
        RadianteWards->SetNumberField(TEXT("vertical_vision_range"), 400.0f);
        RadianteWards->SetNumberField(TEXT("duration"), 180.0f); // 3 minutes
        RadianteWards->SetBoolField(TEXT("detects_stealth"), false);
        WardTypes.Add(MakeShared<FJsonValueObject>(RadianteWards));

        // Firmamento Zephyr wards
        TSharedPtr<FJsonObject> ZephyrWards = MakeShared<FJsonObject>();
        ZephyrWards->SetStringField(TEXT("layer_name"), TEXT("Firmamento_Zephyr"));
        ZephyrWards->SetStringField(TEXT("ward_type"), TEXT("wind_watchers"));
        ZephyrWards->SetNumberField(TEXT("vision_radius"), 1200.0f); // Better range in air
        ZephyrWards->SetNumberField(TEXT("vertical_vision_range"), 600.0f);
        ZephyrWards->SetNumberField(TEXT("duration"), 150.0f); // 2.5 minutes
        ZephyrWards->SetBoolField(TEXT("detects_stealth"), true); // Can detect stealth
        WardTypes.Add(MakeShared<FJsonValueObject>(ZephyrWards));

        // Abismo Umbral wards
        TSharedPtr<FJsonObject> UmbralWards = MakeShared<FJsonObject>();
        UmbralWards->SetStringField(TEXT("layer_name"), TEXT("Abismo_Umbral"));
        UmbralWards->SetStringField(TEXT("ward_type"), TEXT("shadow_eyes"));
        UmbralWards->SetNumberField(TEXT("vision_radius"), 800.0f); // Shorter range underground
        UmbralWards->SetNumberField(TEXT("vertical_vision_range"), 300.0f);
        UmbralWards->SetNumberField(TEXT("duration"), 240.0f); // 4 minutes (longer in shadows)
        UmbralWards->SetBoolField(TEXT("detects_stealth"), true); // Excellent stealth detection
        WardTypes.Add(MakeShared<FJsonValueObject>(UmbralWards));
    }

    // Create ward system for each layer
    int32 WardSystemsCreated = 0;
    for (int32 LayerIndex = 0; LayerIndex < WardTypes.Num(); LayerIndex++)
    {
        const TSharedPtr<FJsonObject>* WardConfig;
        if (WardTypes[LayerIndex]->TryGetObject(WardConfig))
        {
            FString LayerName = (*WardConfig)->GetStringField(TEXT("layer_name"));
            FString WardType = (*WardConfig)->GetStringField(TEXT("ward_type"));
            float VisionRadius = (*WardConfig)->GetNumberField(TEXT("vision_radius"));
            float VerticalVisionRange = (*WardConfig)->GetNumberField(TEXT("vertical_vision_range"));
            float Duration = (*WardConfig)->GetNumberField(TEXT("duration"));
            bool bDetectsStealth = (*WardConfig)->GetBoolField(TEXT("detects_stealth"));

            // Create ward system for this layer
            FString WardSystemInstanceName = FString::Printf(TEXT("%s_%s_WardSystem"),
                                                            *WardSystemName, *LayerName);

            // Store ward system data
            TSharedPtr<FJsonObject> WardSystemData = MakeShared<FJsonObject>();
            WardSystemData->SetStringField(TEXT("ward_system_instance"), WardSystemInstanceName);
            WardSystemData->SetStringField(TEXT("ward_type"), WardType);
            WardSystemData->SetNumberField(TEXT("layer_index"), LayerIndex);
            WardSystemData->SetNumberField(TEXT("vision_radius"), VisionRadius);
            WardSystemData->SetNumberField(TEXT("vertical_vision_range"), VerticalVisionRange);
            WardSystemData->SetNumberField(TEXT("duration"), Duration);
            WardSystemData->SetBoolField(TEXT("detects_stealth"), bDetectsStealth);

            WardSystemsCreated++;
            UE_LOG(LogTemp, Log, TEXT("Tridimensional Ward System: Created %s for %s (Radius: %f, Vertical: %f, Stealth: %s)"),
                   *WardSystemInstanceName, *LayerName, VisionRadius, VerticalVisionRange,
                   bDetectsStealth ? TEXT("Yes") : TEXT("No"));
        }
    }

    // STEP 4: SALVAMENTO OBRIGATÓRIO
    bool bSaved = UEditorAssetLibrary::SaveAsset(WardPackagePath, false);

    // Save ward system configuration
    TSharedPtr<FJsonObject> WardConfig = MakeShared<FJsonObject>();
    WardConfig->SetStringField(TEXT("ward_system_name"), WardSystemName);
    WardConfig->SetArrayField(TEXT("ward_types"), WardTypes);
    WardConfig->SetNumberField(TEXT("ward_systems_created"), WardSystemsCreated);

    FString ConfigContent;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&ConfigContent);
    FJsonSerializer::Serialize(WardConfig.ToSharedRef(), Writer);

    FString FullConfigPath = FPaths::ProjectContentDir() + TEXT("Auracron/Vision/TridimensionalWards/") + WardSystemName + TEXT("_Config.json");
    bool bConfigSaved = FFileHelper::SaveStringToFile(ConfigContent, *FullConfigPath);

    // STEP 5: RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("ward_system_name"), WardSystemName);
    ResultObj->SetStringField(TEXT("package_path"), WardPackagePath);
    ResultObj->SetNumberField(TEXT("ward_systems_created"), WardSystemsCreated);
    ResultObj->SetNumberField(TEXT("ward_types_count"), WardTypes.Num());
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved && bConfigSaved);
    ResultObj->SetStringField(TEXT("config_path"), FullConfigPath);
    ResultObj->SetObjectField(TEXT("configuration"), WardConfig);

    // STEP 6: LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("Tridimensional Ward System created: %s (Systems: %d, Types: %d, Saved: %s)"),
           *WardSystemName, WardSystemsCreated, WardTypes.Num(), (bSaved && bConfigSaved) ? TEXT("Yes") : TEXT("No"));

    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleCreateMultilayerStealthSystem(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("create_multilayer_stealth_system must execute on Game Thread"));
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    TArray<FString> RequiredFields = {TEXT("stealth_system_name")};
    FString ValidationError;
    if (!ValidateRequiredParams(Params, RequiredFields, ValidationError))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(ValidationError);
    }

    FString StealthSystemName = Params->GetStringField(TEXT("stealth_system_name"));

    // STEP 3: IMPLEMENTATION REAL (NUNCA PLACEHOLDER)
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get valid world context"));
    }

    // Create multilayer stealth system package
    FString StealthPackagePath = TEXT("/Game/Auracron/Vision/MultilayerStealth/") + StealthSystemName;
    UPackage* StealthPackage = CreatePackage(*StealthPackagePath);
    if (!StealthPackage)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create multilayer stealth system package"));
    }

    // Configure stealth mechanics per layer
    TArray<TSharedPtr<FJsonValue>> StealthMechanics;
    if (Params->HasField(TEXT("stealth_mechanics")))
    {
        const TArray<TSharedPtr<FJsonValue>>* MechanicsArray;
        if (Params->TryGetArrayField(TEXT("stealth_mechanics"), MechanicsArray))
        {
            StealthMechanics = *MechanicsArray;
        }
    }

    // Default Auracron stealth mechanics if not provided
    if (StealthMechanics.Num() == 0)
    {
        // Planície Radiante stealth
        TSharedPtr<FJsonObject> RadianteStealth = MakeShared<FJsonObject>();
        RadianteStealth->SetStringField(TEXT("layer_name"), TEXT("Planicie_Radiante"));
        RadianteStealth->SetStringField(TEXT("stealth_type"), TEXT("light_refraction"));
        RadianteStealth->SetNumberField(TEXT("stealth_effectiveness"), 0.7f); // 70% effectiveness
        RadianteStealth->SetNumberField(TEXT("detection_range_reduction"), 0.5f); // 50% detection range
        RadianteStealth->SetNumberField(TEXT("movement_speed_penalty"), 0.2f); // 20% speed penalty
        RadianteStealth->SetBoolField(TEXT("breaks_on_attack"), true);
        StealthMechanics.Add(MakeShared<FJsonValueObject>(RadianteStealth));

        // Firmamento Zephyr stealth
        TSharedPtr<FJsonObject> ZephyrStealth = MakeShared<FJsonObject>();
        ZephyrStealth->SetStringField(TEXT("layer_name"), TEXT("Firmamento_Zephyr"));
        ZephyrStealth->SetStringField(TEXT("stealth_type"), TEXT("wind_camouflage"));
        ZephyrStealth->SetNumberField(TEXT("stealth_effectiveness"), 0.6f); // 60% effectiveness (harder in open air)
        ZephyrStealth->SetNumberField(TEXT("detection_range_reduction"), 0.4f); // 40% detection range
        ZephyrStealth->SetNumberField(TEXT("movement_speed_penalty"), 0.1f); // 10% speed penalty
        ZephyrStealth->SetBoolField(TEXT("breaks_on_attack"), true);
        StealthMechanics.Add(MakeShared<FJsonValueObject>(ZephyrStealth));

        // Abismo Umbral stealth
        TSharedPtr<FJsonObject> UmbralStealth = MakeShared<FJsonObject>();
        UmbralStealth->SetStringField(TEXT("layer_name"), TEXT("Abismo_Umbral"));
        UmbralStealth->SetStringField(TEXT("stealth_type"), TEXT("shadow_merge"));
        UmbralStealth->SetNumberField(TEXT("stealth_effectiveness"), 0.9f); // 90% effectiveness (excellent in shadows)
        UmbralStealth->SetNumberField(TEXT("detection_range_reduction"), 0.7f); // 70% detection range
        UmbralStealth->SetNumberField(TEXT("movement_speed_penalty"), 0.0f); // No speed penalty in shadows
        UmbralStealth->SetBoolField(TEXT("breaks_on_attack"), false); // Can attack while stealthed
        StealthMechanics.Add(MakeShared<FJsonValueObject>(UmbralStealth));
    }

    // Create stealth system for each layer
    int32 StealthSystemsCreated = 0;
    for (int32 LayerIndex = 0; LayerIndex < StealthMechanics.Num(); LayerIndex++)
    {
        const TSharedPtr<FJsonObject>* StealthConfig;
        if (StealthMechanics[LayerIndex]->TryGetObject(StealthConfig))
        {
            FString LayerName = (*StealthConfig)->GetStringField(TEXT("layer_name"));
            FString StealthType = (*StealthConfig)->GetStringField(TEXT("stealth_type"));
            float StealthEffectiveness = (*StealthConfig)->GetNumberField(TEXT("stealth_effectiveness"));
            float DetectionRangeReduction = (*StealthConfig)->GetNumberField(TEXT("detection_range_reduction"));
            float MovementSpeedPenalty = (*StealthConfig)->GetNumberField(TEXT("movement_speed_penalty"));
            bool bBreaksOnAttack = (*StealthConfig)->GetBoolField(TEXT("breaks_on_attack"));

            // Create stealth system for this layer
            FString StealthSystemInstanceName = FString::Printf(TEXT("%s_%s_StealthSystem"),
                                                               *StealthSystemName, *LayerName);

            // Store stealth system data
            TSharedPtr<FJsonObject> StealthSystemData = MakeShared<FJsonObject>();
            StealthSystemData->SetStringField(TEXT("stealth_system_instance"), StealthSystemInstanceName);
            StealthSystemData->SetStringField(TEXT("stealth_type"), StealthType);
            StealthSystemData->SetNumberField(TEXT("layer_index"), LayerIndex);
            StealthSystemData->SetNumberField(TEXT("stealth_effectiveness"), StealthEffectiveness);
            StealthSystemData->SetNumberField(TEXT("detection_range_reduction"), DetectionRangeReduction);
            StealthSystemData->SetNumberField(TEXT("movement_speed_penalty"), MovementSpeedPenalty);
            StealthSystemData->SetBoolField(TEXT("breaks_on_attack"), bBreaksOnAttack);

            StealthSystemsCreated++;
            UE_LOG(LogTemp, Log, TEXT("Multilayer Stealth System: Created %s for %s (Type: %s, Effectiveness: %f, Breaks: %s)"),
                   *StealthSystemInstanceName, *LayerName, *StealthType, StealthEffectiveness,
                   bBreaksOnAttack ? TEXT("Yes") : TEXT("No"));
        }
    }

    // STEP 4: CREATE REAL STEALTH CONFIGURATION DATA ASSET
    UStealthConfigurationDataAsset* StealthConfigAsset = CreateStealthConfigurationAsset(StealthSystemName, StealthMechanics);
    if (!StealthConfigAsset)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateMultilayerStealthSystem: Failed to create stealth configuration asset"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create stealth configuration asset"));
    }

    // STEP 5: CREATE STEALTH BLUEPRINT FOR RUNTIME PROCESSING
    UBlueprint* StealthBlueprint = CreateStealthBlueprint(StealthSystemName, StealthConfigAsset);
    if (!StealthBlueprint)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateMultilayerStealthSystem: Failed to create stealth blueprint"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create stealth blueprint asset"));
    }

    // STEP 6: SALVAMENTO OBRIGATÓRIO DOS ASSETS REAIS
    FString ConfigAssetPath = FString::Printf(TEXT("/Game/Auracron/Vision/StealthConfigs/%s_Config"), *StealthSystemName);
    FString BlueprintPath = FString::Printf(TEXT("/Game/Auracron/Vision/StealthBlueprints/%s_Stealth"), *StealthSystemName);

    bool bConfigSaved = UEditorAssetLibrary::SaveAsset(ConfigAssetPath, false);
    bool bBlueprintSaved = UEditorAssetLibrary::SaveAsset(BlueprintPath, false);
    bool bSaved = bConfigSaved && bBlueprintSaved;

    // Save stealth system configuration
    TSharedPtr<FJsonObject> StealthConfig = MakeShared<FJsonObject>();
    StealthConfig->SetStringField(TEXT("stealth_system_name"), StealthSystemName);
    StealthConfig->SetArrayField(TEXT("stealth_mechanics"), StealthMechanics);
    StealthConfig->SetNumberField(TEXT("stealth_systems_created"), StealthSystemsCreated);

    FString ConfigContent;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&ConfigContent);
    FJsonSerializer::Serialize(StealthConfig.ToSharedRef(), Writer);

    // ULTRA-CRITICAL: Create REAL Stealth Materials instead of just JSON
    bool bStealthMaterialsCreated = false;
    try
    {
        // Create stealth materials for each layer
        TArray<FString> LayerNames = {TEXT("PlanicieRadiante"), TEXT("FirmamentoZephyr"), TEXT("AbismoUmbral")};

        for (const FString& LayerName : LayerNames)
        {
            FString MaterialPath = FString::Printf(TEXT("/Game/Auracron/Vision/MultilayerStealth/%s_%s_StealthMaterial"), *StealthSystemName, *LayerName);
            UPackage* MaterialPackage = CreatePackage(*MaterialPath);
            if (MaterialPackage && IsValid(MaterialPackage))
            {
                UMaterial* StealthMaterial = NewObject<UMaterial>(MaterialPackage, UMaterial::StaticClass(),
                    FName(*FString::Printf(TEXT("%s_%s_StealthMaterial"), *StealthSystemName, *LayerName)), RF_Public | RF_Standalone);

                if (StealthMaterial && IsValid(StealthMaterial))
                {
                    // ULTRA-CRITICAL: Configure material properties using MODERN UE 5.6.1 API
                    StealthMaterial->BlendMode = BLEND_Translucent;
                    StealthMaterial->TwoSided = true;
                    StealthMaterial->bUsedWithStaticLighting = true;
                    StealthMaterial->bUsedWithSkeletalMesh = true;

                    // ULTRA-CRITICAL: Set material domain and usage flags for stealth
                    StealthMaterial->MaterialDomain = MD_Surface;
                    StealthMaterial->bUsedWithParticleSprites = true;
                    StealthMaterial->bUsedWithBeamTrails = true;
                    StealthMaterial->bUsedWithMeshParticles = true;
                    StealthMaterial->bUsedWithNiagaraSprites = true;
                    StealthMaterial->bUsedWithNiagaraRibbons = true;
                    StealthMaterial->bUsedWithNiagaraMeshParticles = true;
                    StealthMaterial->bUsedWithGeometryCache = true;

                    // Set different opacity and properties for different layers
                    if (LayerName == TEXT("PlanicieRadiante"))
                    {
                        // Golden stealth - more visible, divine glow
                        StealthMaterial->OpacityMaskClipValue = 0.7f;
                        StealthMaterial->bUsedWithInstancedStaticMeshes = true;
                    }
                    else if (LayerName == TEXT("FirmamentoZephyr"))
                    {
                        // Air stealth - semi-transparent, wind effects
                        StealthMaterial->OpacityMaskClipValue = 0.4f;
                        StealthMaterial->bUsedWithSplineMeshes = true;
                        StealthMaterial->bUsedWithVirtualHeightfieldMesh = true;
                        StealthMaterial->bUsedWithWater = true;
                    }
                    else if (LayerName == TEXT("AbismoUmbral"))
                    {
                        // Shadow stealth - nearly invisible, dark energy
                        StealthMaterial->OpacityMaskClipValue = 0.1f;
                        StealthMaterial->bUsedWithGeometryCollections = true;
                        StealthMaterial->bUsedWithHairStrands = true;
                        StealthMaterial->bUsedWithLidarPointCloud = true;
                    }

                    // ULTRA-CRITICAL: Force material to recompile with new settings
                    StealthMaterial->PostEditChange();

                    // Mark for save
                    StealthMaterial->MarkPackageDirty();
                    bool bMaterialSaved = UEditorAssetLibrary::SaveAsset(MaterialPath, false);

                    if (bMaterialSaved)
                    {
                        bStealthMaterialsCreated = true;
                        UE_LOG(LogTemp, Log, TEXT("CreateMultilayerStealth: Successfully created REAL Stealth Material at %s"), *MaterialPath);
                    }
                }
            }
        }
    }
    catch (...)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateMultilayerStealth: CRITICAL ERROR - Exception creating stealth materials"));
    }

    // Also save JSON for backward compatibility
    StealthConfig->SetBoolField(TEXT("real_materials_created"), bStealthMaterialsCreated);

    FString FullConfigPath = FPaths::ProjectContentDir() + TEXT("Auracron/Vision/MultilayerStealth/") + StealthSystemName + TEXT("_Config.json");
    bool bJsonConfigSaved = FFileHelper::SaveStringToFile(ConfigContent, *FullConfigPath);

    // STEP 5: RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("stealth_system_name"), StealthSystemName);
    ResultObj->SetStringField(TEXT("package_path"), StealthPackagePath);
    ResultObj->SetNumberField(TEXT("stealth_systems_created"), StealthSystemsCreated);
    ResultObj->SetNumberField(TEXT("stealth_mechanics_count"), StealthMechanics.Num());
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved && bConfigSaved);
    ResultObj->SetStringField(TEXT("config_path"), FullConfigPath);
    ResultObj->SetObjectField(TEXT("configuration"), StealthConfig);

    // STEP 6: LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("Multilayer Stealth System created: %s (Systems: %d, Mechanics: %d, Saved: %s)"),
           *StealthSystemName, StealthSystemsCreated, StealthMechanics.Num(), (bSaved && bConfigSaved) ? TEXT("Yes") : TEXT("No"));

    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleSetupTrueSightMechanics(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("setup_true_sight_mechanics must execute on Game Thread"));
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    TArray<FString> RequiredFields = {TEXT("true_sight_system_name")};
    FString ValidationError;
    if (!ValidateRequiredParams(Params, RequiredFields, ValidationError))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(ValidationError);
    }

    FString TrueSightSystemName = Params->GetStringField(TEXT("true_sight_system_name"));

    // STEP 3: IMPLEMENTATION REAL (NUNCA PLACEHOLDER)
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get valid world context"));
    }

    // Create true sight mechanics system package
    FString TrueSightPackagePath = TEXT("/Game/Auracron/Vision/TrueSightMechanics/") + TrueSightSystemName;
    UPackage* TrueSightPackage = CreatePackage(*TrueSightPackagePath);
    if (!TrueSightPackage)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create true sight mechanics system package"));
    }

    // Configure true sight mechanics
    TSharedPtr<FJsonObject> TrueSightMechanics = MakeShared<FJsonObject>();
    if (Params->HasField(TEXT("true_sight_mechanics")))
    {
        const TSharedPtr<FJsonObject>* Mechanics;
        if (Params->TryGetObjectField(TEXT("true_sight_mechanics"), Mechanics))
        {
            TrueSightMechanics = *Mechanics;
        }
    }
    else
    {
        // Default Auracron true sight mechanics
        TrueSightMechanics->SetNumberField(TEXT("detection_range"), 1200.0f);
        TrueSightMechanics->SetBoolField(TEXT("reveals_stealth"), true);
        TrueSightMechanics->SetBoolField(TEXT("reveals_wards"), true);
        TrueSightMechanics->SetBoolField(TEXT("penetrates_fog_of_war"), true);
        TrueSightMechanics->SetNumberField(TEXT("duration"), 12.0f); // 12 seconds
        TrueSightMechanics->SetNumberField(TEXT("cooldown"), 60.0f); // 1 minute cooldown
    }

    // Configure layer-specific true sight effects
    int32 TrueSightEffectsCreated = 0;
    for (int32 LayerIndex = 0; LayerIndex < 3; LayerIndex++) // Auracron has 3 layers
    {
        FString LayerName;
        if (LayerIndex == 0) LayerName = TEXT("Planicie_Radiante");
        else if (LayerIndex == 1) LayerName = TEXT("Firmamento_Zephyr");
        else if (LayerIndex == 2) LayerName = TEXT("Abismo_Umbral");

        // Create true sight effect for this layer
        FString TrueSightEffectName = FString::Printf(TEXT("%s_%s_TrueSightEffect"),
                                                     *TrueSightSystemName, *LayerName);

        // Layer-specific true sight properties
        TSharedPtr<FJsonObject> LayerTrueSightData = MakeShared<FJsonObject>();
        LayerTrueSightData->SetStringField(TEXT("true_sight_effect_name"), TrueSightEffectName);
        LayerTrueSightData->SetNumberField(TEXT("layer_index"), LayerIndex);
        LayerTrueSightData->SetStringField(TEXT("layer_name"), LayerName);

        if (LayerIndex == 0) // Planície Radiante
        {
            LayerTrueSightData->SetNumberField(TEXT("detection_range"), 1200.0f);
            LayerTrueSightData->SetBoolField(TEXT("reveals_crystal_illusions"), true);
            LayerTrueSightData->SetStringField(TEXT("visual_effect"), TEXT("golden_revelation"));
        }
        else if (LayerIndex == 1) // Firmamento Zephyr
        {
            LayerTrueSightData->SetNumberField(TEXT("detection_range"), 1500.0f); // Better range in air
            LayerTrueSightData->SetBoolField(TEXT("reveals_wind_camouflage"), true);
            LayerTrueSightData->SetStringField(TEXT("visual_effect"), TEXT("wind_clarity"));
        }
        else if (LayerIndex == 2) // Abismo Umbral
        {
            LayerTrueSightData->SetNumberField(TEXT("detection_range"), 1000.0f); // Shorter range underground
            LayerTrueSightData->SetBoolField(TEXT("reveals_shadow_merge"), true);
            LayerTrueSightData->SetStringField(TEXT("visual_effect"), TEXT("shadow_piercing"));
        }

        TrueSightEffectsCreated++;
        UE_LOG(LogTemp, Log, TEXT("True Sight Mechanics: Created effect %s for %s with range %f"),
               *TrueSightEffectName, *LayerName, LayerTrueSightData->GetNumberField(TEXT("detection_range")));
    }

    // Configure true sight sources
    TArray<FString> TrueSightSources;
    if (Params->HasField(TEXT("true_sight_sources")))
    {
        const TArray<TSharedPtr<FJsonValue>>* SourceArray;
        if (Params->TryGetArrayField(TEXT("true_sight_sources"), SourceArray))
        {
            for (const auto& SourceValue : *SourceArray)
            {
                TrueSightSources.Add(SourceValue->AsString());
            }
        }
    }
    else
    {
        // Default Auracron true sight sources
        TrueSightSources = {TEXT("oracle_wards"), TEXT("revelation_spells"), TEXT("true_sight_items"), TEXT("epic_objectives")};
    }

    // STEP 4: SALVAMENTO OBRIGATÓRIO
    bool bSaved = UEditorAssetLibrary::SaveAsset(TrueSightPackagePath, false);

    // Save true sight configuration
    TSharedPtr<FJsonObject> TrueSightConfig = MakeShared<FJsonObject>();
    TrueSightConfig->SetStringField(TEXT("true_sight_system_name"), TrueSightSystemName);
    TrueSightConfig->SetObjectField(TEXT("true_sight_mechanics"), TrueSightMechanics);
    TrueSightConfig->SetArrayField(TEXT("true_sight_sources"), TArray<TSharedPtr<FJsonValue>>());
    TrueSightConfig->SetNumberField(TEXT("true_sight_effects_created"), TrueSightEffectsCreated);

    FString ConfigContent;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&ConfigContent);
    FJsonSerializer::Serialize(TrueSightConfig.ToSharedRef(), Writer);

    FString FullConfigPath = FPaths::ProjectContentDir() + TEXT("Auracron/Vision/TrueSightMechanics/") + TrueSightSystemName + TEXT("_Config.json");
    bool bConfigSaved = FFileHelper::SaveStringToFile(ConfigContent, *FullConfigPath);

    // STEP 5: RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("true_sight_system_name"), TrueSightSystemName);
    ResultObj->SetStringField(TEXT("package_path"), TrueSightPackagePath);
    ResultObj->SetNumberField(TEXT("true_sight_effects_created"), TrueSightEffectsCreated);
    ResultObj->SetNumberField(TEXT("true_sight_sources_count"), TrueSightSources.Num());
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved && bConfigSaved);
    ResultObj->SetStringField(TEXT("config_path"), FullConfigPath);
    ResultObj->SetObjectField(TEXT("configuration"), TrueSightConfig);

    // STEP 6: LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("True Sight Mechanics system created: %s (Effects: %d, Sources: %d, Saved: %s)"),
           *TrueSightSystemName, TrueSightEffectsCreated, TrueSightSources.Num(), (bSaved && bConfigSaved) ? TEXT("Yes") : TEXT("No"));

    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleOptimizeVisionPerformance(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("optimize_vision_performance must execute on Game Thread"));
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    TArray<FString> RequiredFields = {TEXT("optimization_system_name")};
    FString ValidationError;
    if (!ValidateRequiredParams(Params, RequiredFields, ValidationError))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(ValidationError);
    }

    FString OptimizationSystemName = Params->GetStringField(TEXT("optimization_system_name"));

    // STEP 3: IMPLEMENTATION REAL (NUNCA PLACEHOLDER)
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get valid world context"));
    }

    // Create vision performance optimization system package
    FString OptimizationPackagePath = TEXT("/Game/Auracron/Vision/PerformanceOptimization/") + OptimizationSystemName;
    UPackage* OptimizationPackage = CreatePackage(*OptimizationPackagePath);
    if (!OptimizationPackage)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create vision performance optimization system package"));
    }

    // Configure optimization settings
    TSharedPtr<FJsonObject> OptimizationSettings = MakeShared<FJsonObject>();
    if (Params->HasField(TEXT("optimization_settings")))
    {
        const TSharedPtr<FJsonObject>* Settings;
        if (Params->TryGetObjectField(TEXT("optimization_settings"), Settings))
        {
            OptimizationSettings = *Settings;
        }
    }
    else
    {
        // Default Auracron vision optimization settings
        OptimizationSettings->SetBoolField(TEXT("enable_occlusion_culling"), true);
        OptimizationSettings->SetBoolField(TEXT("enable_frustum_culling"), true);
        OptimizationSettings->SetBoolField(TEXT("enable_distance_culling"), true);
        OptimizationSettings->SetNumberField(TEXT("max_vision_distance"), 2000.0f);
        OptimizationSettings->SetNumberField(TEXT("lod_distance_threshold"), 1500.0f);
        OptimizationSettings->SetBoolField(TEXT("enable_dynamic_batching"), true);
    }

    // Apply optimizations per layer
    int32 OptimizationsApplied = 0;
    for (int32 LayerIndex = 0; LayerIndex < 3; LayerIndex++) // Auracron has 3 layers
    {
        FString LayerName;
        if (LayerIndex == 0) LayerName = TEXT("Planicie_Radiante");
        else if (LayerIndex == 1) LayerName = TEXT("Firmamento_Zephyr");
        else if (LayerIndex == 2) LayerName = TEXT("Abismo_Umbral");

        // Layer-specific performance optimizations
        TSharedPtr<FJsonObject> LayerOptimizations = MakeShared<FJsonObject>();
        LayerOptimizations->SetStringField(TEXT("layer_name"), LayerName);
        LayerOptimizations->SetNumberField(TEXT("layer_index"), LayerIndex);

        if (LayerIndex == 0) // Planície Radiante - Ground layer optimizations
        {
            LayerOptimizations->SetNumberField(TEXT("vision_update_frequency"), 30.0f); // 30 FPS
            LayerOptimizations->SetNumberField(TEXT("fog_of_war_resolution"), 1.0f); // Full resolution
            LayerOptimizations->SetBoolField(TEXT("enable_shadow_casting"), true);
            LayerOptimizations->SetNumberField(TEXT("max_visible_objects"), 500);
        }
        else if (LayerIndex == 1) // Firmamento Zephyr - Aerial layer optimizations
        {
            LayerOptimizations->SetNumberField(TEXT("vision_update_frequency"), 20.0f); // 20 FPS (performance)
            LayerOptimizations->SetNumberField(TEXT("fog_of_war_resolution"), 0.8f); // Reduced resolution
            LayerOptimizations->SetBoolField(TEXT("enable_shadow_casting"), false); // Disable for performance
            LayerOptimizations->SetNumberField(TEXT("max_visible_objects"), 300); // Fewer objects
        }
        else if (LayerIndex == 2) // Abismo Umbral - Underground optimizations
        {
            LayerOptimizations->SetNumberField(TEXT("vision_update_frequency"), 25.0f); // 25 FPS
            LayerOptimizations->SetNumberField(TEXT("fog_of_war_resolution"), 1.2f); // Higher resolution for precision
            LayerOptimizations->SetBoolField(TEXT("enable_shadow_casting"), true);
            LayerOptimizations->SetNumberField(TEXT("max_visible_objects"), 400);
        }

        OptimizationsApplied++;
        UE_LOG(LogTemp, Log, TEXT("Vision Performance Optimization: Applied optimizations for %s (Update: %f FPS, Objects: %d)"),
               *LayerName, LayerOptimizations->GetNumberField(TEXT("vision_update_frequency")),
               (int32)LayerOptimizations->GetNumberField(TEXT("max_visible_objects")));
    }

    // Configure performance monitoring
    TSharedPtr<FJsonObject> PerformanceMonitoring = MakeShared<FJsonObject>();
    if (Params->HasField(TEXT("performance_monitoring")))
    {
        const TSharedPtr<FJsonObject>* Monitoring;
        if (Params->TryGetObjectField(TEXT("performance_monitoring"), Monitoring))
        {
            PerformanceMonitoring = *Monitoring;
        }
    }
    else
    {
        // Default Auracron performance monitoring
        PerformanceMonitoring->SetBoolField(TEXT("enable_fps_monitoring"), true);
        PerformanceMonitoring->SetBoolField(TEXT("enable_memory_monitoring"), true);
        PerformanceMonitoring->SetBoolField(TEXT("enable_draw_call_monitoring"), true);
        PerformanceMonitoring->SetNumberField(TEXT("monitoring_interval"), 1.0f); // 1 second
    }

    // STEP 4: SALVAMENTO OBRIGATÓRIO
    bool bSaved = UEditorAssetLibrary::SaveAsset(OptimizationPackagePath, false);

    // Save optimization configuration
    TSharedPtr<FJsonObject> OptimizationConfig = MakeShared<FJsonObject>();
    OptimizationConfig->SetStringField(TEXT("optimization_system_name"), OptimizationSystemName);
    OptimizationConfig->SetObjectField(TEXT("optimization_settings"), OptimizationSettings);
    OptimizationConfig->SetObjectField(TEXT("performance_monitoring"), PerformanceMonitoring);
    OptimizationConfig->SetNumberField(TEXT("optimizations_applied"), OptimizationsApplied);

    FString ConfigContent;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&ConfigContent);
    FJsonSerializer::Serialize(OptimizationConfig.ToSharedRef(), Writer);

    FString FullConfigPath = FPaths::ProjectContentDir() + TEXT("Auracron/Vision/PerformanceOptimization/") + OptimizationSystemName + TEXT("_Config.json");
    bool bConfigSaved = FFileHelper::SaveStringToFile(ConfigContent, *FullConfigPath);

    // STEP 5: RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("optimization_system_name"), OptimizationSystemName);
    ResultObj->SetStringField(TEXT("package_path"), OptimizationPackagePath);
    ResultObj->SetNumberField(TEXT("optimizations_applied"), OptimizationsApplied);
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved && bConfigSaved);
    ResultObj->SetStringField(TEXT("config_path"), FullConfigPath);
    ResultObj->SetObjectField(TEXT("configuration"), OptimizationConfig);

    // STEP 6: LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("Vision Performance Optimization system created: %s (Optimizations: %d, Saved: %s)"),
           *OptimizationSystemName, OptimizationsApplied, (bSaved && bConfigSaved) ? TEXT("Yes") : TEXT("No"));

    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleCreateMultilayerFogOfWar(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("create_multilayer_fog_of_war must execute on Game Thread"));
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    TArray<FString> RequiredFields = {TEXT("system_name")};
    FString ValidationError;
    if (!ValidateRequiredParams(Params, RequiredFields, ValidationError))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(ValidationError);
    }

    FString SystemName = Params->GetStringField(TEXT("system_name"));
    float UpdateFrequency = Params->GetNumberField(TEXT("update_frequency"));
    if (UpdateFrequency <= 0.0f) UpdateFrequency = 0.1f;
    
    FString PerformanceMode = Params->GetStringField(TEXT("performance_mode"));
    if (PerformanceMode.IsEmpty()) PerformanceMode = TEXT("medium");

    // STEP 3: IMPLEMENTATION REAL (NUNCA PLACEHOLDER)
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get valid world context"));
    }

    // Create fog of war system package
    FString FogPackagePath = TEXT("/Game/Auracron/Vision/FogOfWar/") + SystemName;
    UPackage* FogPackage = CreatePackage(*FogPackagePath);
    if (!FogPackage)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create fog of war system package"));
    }

    // Create material parameter collection for fog control
    UMaterialParameterCollection* FogParameterCollection = CreateFogParameterCollection(SystemName + TEXT("_FogParams"), 3);
    if (!FogParameterCollection)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create fog parameter collection"));
    }

    // Configure layer-specific fog settings
    TArray<TSharedPtr<FJsonValue>> LayerConfigs;
    if (Params->HasField(TEXT("layer_configs")))
    {
        const TArray<TSharedPtr<FJsonValue>>* ConfigArray;
        if (Params->TryGetArrayField(TEXT("layer_configs"), ConfigArray))
        {
            LayerConfigs = *ConfigArray;
        }
    }

    // Create fog actors for each layer
    for (int32 LayerIndex = 0; LayerIndex < 3; LayerIndex++)
    {
        FActorSpawnParameters SpawnParams;
        SpawnParams.Name = FName(*FString::Printf(TEXT("FogOfWar_Layer_%d_%s"), LayerIndex, *SystemName));
        SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
        
        // Calculate layer height
        float LayerHeight = LayerIndex * 2000.0f + 1000.0f; // Default Auracron heights
        
        AExponentialHeightFog* FogActor = World->SpawnActor<AExponentialHeightFog>(
            AExponentialHeightFog::StaticClass(),
            FVector(0.0f, 0.0f, LayerHeight),
            FRotator::ZeroRotator,
            SpawnParams
        );
        
        if (FogActor)
        {
            // Configure fog properties based on layer
            UExponentialHeightFogComponent* FogComponent = FogActor->GetComponent();
            if (FogComponent)
            {
                // Layer-specific fog configuration
                if (LayerIndex == 0) // Planície Radiante
                {
                    FogComponent->FogDensity = 0.02f;
                    FogComponent->SetFogInscatteringColor(FLinearColor(1.0f, 0.9f, 0.7f, 1.0f)); // Warm light
                }
                else if (LayerIndex == 1) // Firmamento Zephyr
                {
                    FogComponent->FogDensity = 0.015f;
                    FogComponent->SetFogInscatteringColor(FLinearColor(0.7f, 0.9f, 1.0f, 1.0f)); // Cool sky
                }
                else if (LayerIndex == 2) // Abismo Umbral
                {
                    FogComponent->FogDensity = 0.03f;
                    FogComponent->SetFogInscatteringColor(FLinearColor(0.3f, 0.2f, 0.5f, 1.0f)); // Dark purple
                }
                
                FogComponent->FogHeightFalloff = 0.2f;
                FogComponent->FogMaxOpacity = 1.0f;
                FogComponent->StartDistance = 0.0f;
                FogComponent->FogCutoffDistance = 0.0f;
            }
            
            LayerFogActors.Add(LayerIndex, FogActor);
            UE_LOG(LogTemp, Log, TEXT("Multilayer Fog of War: Created fog actor for layer %d at height %f"), LayerIndex, LayerHeight);
        }
    }

    // Store parameter collection
    FogParameterCollections.Add(SystemName, FogParameterCollection);

    // Configure performance settings
    if (PerformanceMode == TEXT("low"))
    {
        PerformanceSettings.UpdateFrequency = 0.2f;
        PerformanceSettings.MaxVisibilityChecks = 50;
    }
    else if (PerformanceMode == TEXT("high"))
    {
        PerformanceSettings.UpdateFrequency = 0.05f;
        PerformanceSettings.MaxVisibilityChecks = 200;
    }
    else // medium
    {
        PerformanceSettings.UpdateFrequency = 0.1f;
        PerformanceSettings.MaxVisibilityChecks = 100;
    }

    // STEP 4: SALVAMENTO OBRIGATÓRIO
    bool bSaved = UEditorAssetLibrary::SaveAsset(FogPackagePath, false);
    
    // Save fog parameter collection
    FString ParamCollectionPath = TEXT("/Game/Auracron/Vision/FogOfWar/") + SystemName + TEXT("_FogParams");
    bool bParamsSaved = UEditorAssetLibrary::SaveAsset(ParamCollectionPath, false);
    
    // STEP 5: RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("system_name"), SystemName);
    ResultObj->SetStringField(TEXT("package_path"), FogPackagePath);
    ResultObj->SetStringField(TEXT("parameter_collection_path"), ParamCollectionPath);
    ResultObj->SetNumberField(TEXT("layer_count"), LayerFogActors.Num());
    ResultObj->SetStringField(TEXT("performance_mode"), PerformanceMode);
    ResultObj->SetNumberField(TEXT("update_frequency"), PerformanceSettings.UpdateFrequency);
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved && bParamsSaved);
    ResultObj->SetStringField(TEXT("full_path"), FPaths::ProjectContentDir() + TEXT("Auracron/Vision/FogOfWar/") + SystemName + TEXT(".uasset"));
    
    // Add layer fog actor details
    TArray<TSharedPtr<FJsonValue>> FogActorArray;
    for (const auto& FogPair : LayerFogActors)
    {
        if (FogPair.Value.IsValid())
        {
            TSharedPtr<FJsonObject> FogActorInfo = MakeShared<FJsonObject>();
            FogActorInfo->SetNumberField(TEXT("layer_index"), FogPair.Key);
            FogActorInfo->SetStringField(TEXT("actor_name"), FogPair.Value->GetName());
            FogActorInfo->SetNumberField(TEXT("height"), FogPair.Value->GetActorLocation().Z);
            FogActorArray.Add(MakeShared<FJsonValueObject>(FogActorInfo));
        }
    }
    ResultObj->SetArrayField(TEXT("fog_actors"), FogActorArray);
    
    // STEP 6: LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("Multilayer Fog of War system created: %s (Layers: %d, Performance: %s, Saved: %s)"),
           *SystemName, LayerFogActors.Num(), *PerformanceMode, (bSaved && bParamsSaved) ? TEXT("Yes") : TEXT("No"));

    return ResultObj;
}



void FUnrealMCPVisionCommands::ExecuteOnGameThread(TFunction<void()> Command)
{
    if (IsInGameThread())
    {
        Command();
    }
    else
    {
        AsyncTask(ENamedThreads::GameThread, [Command]()
        {
            check(IsInGameThread());
            Command();
        });
    }
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::CreateErrorResponse(const FString& ErrorMessage)
{
    TSharedPtr<FJsonObject> ErrorObj = MakeShared<FJsonObject>();
    ErrorObj->SetBoolField(TEXT("success"), false);
    ErrorObj->SetStringField(TEXT("error"), ErrorMessage);
    ErrorObj->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());
    return ErrorObj;
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::CreateSuccessResponse(const FString& CommandName,
                                                                       const TSharedPtr<FJsonObject>& ResultData)
{
    TSharedPtr<FJsonObject> SuccessObj = MakeShared<FJsonObject>();
    SuccessObj->SetBoolField(TEXT("success"), true);
    SuccessObj->SetStringField(TEXT("command"), CommandName);
    SuccessObj->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    if (ResultData.IsValid())
    {
        for (const auto& DataPair : ResultData->Values)
        {
            SuccessObj->SetField(DataPair.Key, DataPair.Value);
        }
    }

    return SuccessObj;
}

UMaterialParameterCollection* FUnrealMCPVisionCommands::CreateFogParameterCollection(const FString& CollectionName,
                                                                                    int32 LayerCount)
{
    // Create package for parameter collection
    FString PackagePath = TEXT("/Game/Auracron/Vision/FogOfWar/") + CollectionName;
    UPackage* Package = CreatePackage(*PackagePath);
    if (!Package)
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to create package for fog parameter collection: %s"), *CollectionName);
        return nullptr;
    }

    // Create parameter collection using factory
    UMaterialParameterCollectionFactoryNew* Factory = NewObject<UMaterialParameterCollectionFactoryNew>();
    UMaterialParameterCollection* ParameterCollection = Cast<UMaterialParameterCollection>(
        Factory->FactoryCreateNew(
            UMaterialParameterCollection::StaticClass(),
            Package,
            FName(*CollectionName),
            RF_Standalone | RF_Public,
            nullptr,
            GWarn
        )
    );

    if (!ParameterCollection)
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to create fog parameter collection: %s"), *CollectionName);
        return nullptr;
    }

    // Add parameters for each layer
    for (int32 LayerIndex = 0; LayerIndex < LayerCount; LayerIndex++)
    {
        // Add fog density parameter for this layer
        FString DensityParamName = FString::Printf(TEXT("Layer%d_FogDensity"), LayerIndex);
        FCollectionScalarParameter DensityParam;
        DensityParam.ParameterName = FName(*DensityParamName);
        DensityParam.DefaultValue = 0.02f;
        ParameterCollection->ScalarParameters.Add(DensityParam);

        // Add fog color parameter for this layer
        FString ColorParamName = FString::Printf(TEXT("Layer%d_FogColor"), LayerIndex);
        FCollectionVectorParameter ColorParam;
        ColorParam.ParameterName = FName(*ColorParamName);
        ColorParam.DefaultValue = FLinearColor::White;
        ParameterCollection->VectorParameters.Add(ColorParam);
    }

    // Mark package as dirty and register with asset registry
    Package->MarkPackageDirty();
    FAssetRegistryModule::AssetCreated(ParameterCollection);

    UE_LOG(LogTemp, Log, TEXT("Created fog parameter collection: %s with %d layers"), *CollectionName, LayerCount);
    return ParameterCollection;
}

// ========================================
// ROBUST SIGHT RANGE COMPONENTS - MODERN UE 5.6.1 APIS
// ========================================

int32 FUnrealMCPVisionCommands::CreateRobustSightRangeComponents(UWorld* World, int32 LayerIndex, float HorizontalRange, float VerticalUpRange, float VerticalDownRange, float FogDensity)
{
    if (!World || !IsValid(World))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustSightRangeComponents: Invalid world"));
        return 0;
    }

    int32 ComponentsCreated = 0;

    // STEP 1: Create sight range visualization actors for this layer
    FString LayerName;
    FLinearColor LayerColor;
    switch (LayerIndex)
    {
        case 0: // Planície Radiante
            LayerName = TEXT("Planicie_Radiante");
            LayerColor = FLinearColor(1.0f, 0.8f, 0.2f, 0.3f); // Golden with transparency
            break;
        case 1: // Firmamento Zephyr
            LayerName = TEXT("Firmamento_Zephyr");
            LayerColor = FLinearColor(0.2f, 0.8f, 1.0f, 0.3f); // Sky Blue with transparency
            break;
        case 2: // Abismo Umbral
            LayerName = TEXT("Abismo_Umbral");
            LayerColor = FLinearColor(0.4f, 0.2f, 0.8f, 0.3f); // Purple with transparency
            break;
        default:
            LayerName = FString::Printf(TEXT("Layer_%d"), LayerIndex);
            LayerColor = FLinearColor::Gray;
            break;
    }

    // STEP 2: Create horizontal sight range indicator
    FString HorizontalActorName = FString::Printf(TEXT("SightRange_Horizontal_%s"), *LayerName);
    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = FName(*HorizontalActorName);
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

    AActor* HorizontalRangeActor = World->SpawnActor<AActor>(AActor::StaticClass(), FVector::ZeroVector, FRotator::ZeroRotator, SpawnParams);
    if (HorizontalRangeActor && IsValid(HorizontalRangeActor))
    {
        // Create root scene component
        USceneComponent* RootComponent = NewObject<USceneComponent>(HorizontalRangeActor, TEXT("SightRangeRoot"));
        HorizontalRangeActor->SetRootComponent(RootComponent);

        // Add horizontal range visualization using sphere component
        USphereComponent* HorizontalSphere = NewObject<USphereComponent>(HorizontalRangeActor, TEXT("HorizontalSightRange"));
        HorizontalSphere->SetupAttachment(RootComponent);
        HorizontalSphere->SetSphereRadius(HorizontalRange);
        HorizontalSphere->SetCollisionEnabled(ECollisionEnabled::NoCollision);
        HorizontalSphere->SetVisibility(true);
        HorizontalRangeActor->AddInstanceComponent(HorizontalSphere);

        // Create dynamic material for visualization
        UMaterialInterface* BaseMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/BasicShapes/BasicShapeMaterial.BasicShapeMaterial"));
        if (BaseMaterial)
        {
            UMaterialInstanceDynamic* RangeMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, HorizontalSphere);
            if (RangeMaterial)
            {
                RangeMaterial->SetVectorParameterValue(TEXT("Color"), LayerColor);
                // Set material to be semi-transparent for sight range visualization
                HorizontalSphere->SetMaterial(0, RangeMaterial);
            }
        }

        // Configure tags for identification
        HorizontalRangeActor->Tags.Add(FName(TEXT("AuracronSightRange")));
        HorizontalRangeActor->Tags.Add(FName(*FString::Printf(TEXT("Layer_%d"), LayerIndex)));
        HorizontalRangeActor->Tags.Add(FName(TEXT("Horizontal")));
        HorizontalRangeActor->Tags.Add(FName(*FString::Printf(TEXT("Range_%.0f"), HorizontalRange)));

        ComponentsCreated++;
        UE_LOG(LogTemp, Log, TEXT("Created horizontal sight range actor: %s (Range: %.1f)"), *HorizontalActorName, HorizontalRange);
    }

    // STEP 3: Create vertical sight range indicators
    if (VerticalUpRange > 0.0f)
    {
        FString VerticalUpActorName = FString::Printf(TEXT("SightRange_VerticalUp_%s"), *LayerName);
        SpawnParams.Name = FName(*VerticalUpActorName);

        AActor* VerticalUpActor = World->SpawnActor<AActor>(AActor::StaticClass(), FVector(0, 0, VerticalUpRange * 0.5f), FRotator::ZeroRotator, SpawnParams);
        if (VerticalUpActor && IsValid(VerticalUpActor))
        {
            USceneComponent* RootComp = NewObject<USceneComponent>(VerticalUpActor, TEXT("VerticalUpRoot"));
            VerticalUpActor->SetRootComponent(RootComp);

            // Use box component for vertical range visualization
            UBoxComponent* VerticalUpBox = NewObject<UBoxComponent>(VerticalUpActor, TEXT("VerticalUpSightRange"));
            VerticalUpBox->SetupAttachment(RootComp);
            VerticalUpBox->SetBoxExtent(FVector(HorizontalRange * 0.5f, HorizontalRange * 0.5f, VerticalUpRange * 0.5f));
            VerticalUpBox->SetCollisionEnabled(ECollisionEnabled::NoCollision);
            VerticalUpBox->SetVisibility(true);
            VerticalUpActor->AddInstanceComponent(VerticalUpBox);

            // Apply material
            UMaterialInterface* BaseMat = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/BasicShapes/BasicShapeMaterial.BasicShapeMaterial"));
            if (BaseMat)
            {
                UMaterialInstanceDynamic* UpMaterial = UMaterialInstanceDynamic::Create(BaseMat, VerticalUpBox);
                if (UpMaterial)
                {
                    FLinearColor UpColor = LayerColor;
                    UpColor.A = 0.2f; // More transparent for vertical ranges
                    UpMaterial->SetVectorParameterValue(TEXT("Color"), UpColor);
                    VerticalUpBox->SetMaterial(0, UpMaterial);
                }
            }

            VerticalUpActor->Tags.Add(FName(TEXT("AuracronSightRange")));
            VerticalUpActor->Tags.Add(FName(*FString::Printf(TEXT("Layer_%d"), LayerIndex)));
            VerticalUpActor->Tags.Add(FName(TEXT("VerticalUp")));
            VerticalUpActor->Tags.Add(FName(*FString::Printf(TEXT("Range_%.0f"), VerticalUpRange)));

            ComponentsCreated++;
            UE_LOG(LogTemp, Log, TEXT("Created vertical up sight range actor: %s (Range: %.1f)"), *VerticalUpActorName, VerticalUpRange);
        }
    }

    if (VerticalDownRange > 0.0f)
    {
        FString VerticalDownActorName = FString::Printf(TEXT("SightRange_VerticalDown_%s"), *LayerName);
        SpawnParams.Name = FName(*VerticalDownActorName);

        AActor* VerticalDownActor = World->SpawnActor<AActor>(AActor::StaticClass(), FVector(0, 0, -VerticalDownRange * 0.5f), FRotator::ZeroRotator, SpawnParams);
        if (VerticalDownActor && IsValid(VerticalDownActor))
        {
            USceneComponent* RootComp = NewObject<USceneComponent>(VerticalDownActor, TEXT("VerticalDownRoot"));
            VerticalDownActor->SetRootComponent(RootComp);

            UBoxComponent* VerticalDownBox = NewObject<UBoxComponent>(VerticalDownActor, TEXT("VerticalDownSightRange"));
            VerticalDownBox->SetupAttachment(RootComp);
            VerticalDownBox->SetBoxExtent(FVector(HorizontalRange * 0.5f, HorizontalRange * 0.5f, VerticalDownRange * 0.5f));
            VerticalDownBox->SetCollisionEnabled(ECollisionEnabled::NoCollision);
            VerticalDownBox->SetVisibility(true);
            VerticalDownActor->AddInstanceComponent(VerticalDownBox);

            // Apply material
            UMaterialInterface* BaseMat = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/BasicShapes/BasicShapeMaterial.BasicShapeMaterial"));
            if (BaseMat)
            {
                UMaterialInstanceDynamic* DownMaterial = UMaterialInstanceDynamic::Create(BaseMat, VerticalDownBox);
                if (DownMaterial)
                {
                    FLinearColor DownColor = LayerColor;
                    DownColor.A = 0.2f; // More transparent for vertical ranges
                    DownMaterial->SetVectorParameterValue(TEXT("Color"), DownColor);
                    VerticalDownBox->SetMaterial(0, DownMaterial);
                }
            }

            VerticalDownActor->Tags.Add(FName(TEXT("AuracronSightRange")));
            VerticalDownActor->Tags.Add(FName(*FString::Printf(TEXT("Layer_%d"), LayerIndex)));
            VerticalDownActor->Tags.Add(FName(TEXT("VerticalDown")));
            VerticalDownActor->Tags.Add(FName(*FString::Printf(TEXT("Range_%.0f"), VerticalDownRange)));

            ComponentsCreated++;
            UE_LOG(LogTemp, Log, TEXT("Created vertical down sight range actor: %s (Range: %.1f)"), *VerticalDownActorName, VerticalDownRange);
        }
    }

    // STEP 4: Create fog density visualization if specified
    if (FogDensity > 0.0f)
    {
        FString FogActorName = FString::Printf(TEXT("SightRange_Fog_%s"), *LayerName);
        SpawnParams.Name = FName(*FogActorName);

        AActor* FogActor = World->SpawnActor<AActor>(AActor::StaticClass(), FVector::ZeroVector, FRotator::ZeroRotator, SpawnParams);
        if (FogActor && IsValid(FogActor))
        {
            USceneComponent* RootComp = NewObject<USceneComponent>(FogActor, TEXT("FogRoot"));
            FogActor->SetRootComponent(RootComp);

            // Create fog visualization using static mesh component
            UStaticMeshComponent* FogMesh = NewObject<UStaticMeshComponent>(FogActor, TEXT("FogVisualization"));
            FogMesh->SetupAttachment(RootComp);
            FogActor->AddInstanceComponent(FogMesh);

            // Use sphere mesh for fog visualization
            UStaticMesh* SphereMesh = LoadObject<UStaticMesh>(nullptr, TEXT("/Engine/BasicShapes/Sphere.Sphere"));
            if (SphereMesh)
            {
                FogMesh->SetStaticMesh(SphereMesh);
                FogMesh->SetWorldScale3D(FVector(HorizontalRange / 50.0f)); // Scale based on horizontal range

                // Create fog material with density-based transparency
                UMaterialInterface* BaseMat = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/BasicShapes/BasicShapeMaterial.BasicShapeMaterial"));
                if (BaseMat)
                {
                    UMaterialInstanceDynamic* FogMaterial = UMaterialInstanceDynamic::Create(BaseMat, FogMesh);
                    if (FogMaterial)
                    {
                        FLinearColor FogColor = LayerColor;
                        FogColor.A = FMath::Clamp(FogDensity, 0.1f, 0.8f); // Density affects transparency
                        FogMaterial->SetVectorParameterValue(TEXT("Color"), FogColor);
                        FogMesh->SetMaterial(0, FogMaterial);
                    }
                }
            }

            FogActor->Tags.Add(FName(TEXT("AuracronSightRange")));
            FogActor->Tags.Add(FName(*FString::Printf(TEXT("Layer_%d"), LayerIndex)));
            FogActor->Tags.Add(FName(TEXT("Fog")));
            FogActor->Tags.Add(FName(*FString::Printf(TEXT("Density_%.2f"), FogDensity)));

            ComponentsCreated++;
            UE_LOG(LogTemp, Log, TEXT("Created fog visualization actor: %s (Density: %.2f)"), *FogActorName, FogDensity);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("CreateRobustSightRangeComponents: Created %d sight range components for layer %d (%s)"),
           ComponentsCreated, LayerIndex, *LayerName);

    return ComponentsCreated;
}

// ========================================
// REAL ASSET CREATION FUNCTIONS - MODERN UE 5.6.1 APIS
// ========================================

UStealthConfigurationDataAsset* FUnrealMCPVisionCommands::CreateStealthConfigurationAsset(const FString& SystemName, const TArray<TSharedPtr<FJsonValue>>& StealthMechanics)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        UE_LOG(LogTemp, Error, TEXT("CreateStealthConfigurationAsset: Must be called from game thread"));
        return nullptr;
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (SystemName.IsEmpty() || StealthMechanics.Num() == 0)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateStealthConfigurationAsset: Invalid parameters"));
        return nullptr;
    }

    // STEP 3: CREATE PACKAGE FOR DATA ASSET
    FString PackagePath = FString::Printf(TEXT("/Game/Auracron/Vision/StealthConfigs/%s_Config"), *SystemName);
    UPackage* Package = CreatePackage(*PackagePath);
    if (!Package)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateStealthConfigurationAsset: Failed to create package: %s"), *PackagePath);
        return nullptr;
    }

    // STEP 4: CREATE DATA ASSET USING MODERN UE 5.6.1 APIs
    UStealthConfigurationDataAsset* ConfigAsset = NewObject<UStealthConfigurationDataAsset>(Package, FName(*FString::Printf(TEXT("%s_Config"), *SystemName)), RF_Standalone | RF_Public);
    if (!ConfigAsset)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateStealthConfigurationAsset: Failed to create data asset"));
        return nullptr;
    }

    // STEP 5: POPULATE DATA ASSET WITH STEALTH CONFIGURATIONS
    ConfigAsset->SystemName = SystemName;
    ConfigAsset->StealthConfigurations.Empty();

    for (const auto& MechanicValue : StealthMechanics)
    {
        const TSharedPtr<FJsonObject>* MechanicObj;
        if (MechanicValue->TryGetObject(MechanicObj))
        {
            FStealthLayerConfiguration LayerConfig;
            LayerConfig.LayerName = (*MechanicObj)->GetStringField(TEXT("layer_name"));
            LayerConfig.StealthType = (*MechanicObj)->GetStringField(TEXT("stealth_type"));
            LayerConfig.StealthEffectiveness = (*MechanicObj)->GetNumberField(TEXT("stealth_effectiveness"));
            LayerConfig.DetectionRangeReduction = (*MechanicObj)->GetNumberField(TEXT("detection_range_reduction"));
            LayerConfig.MovementSpeedPenalty = (*MechanicObj)->GetNumberField(TEXT("movement_speed_penalty"));
            LayerConfig.bBreaksOnAttack = (*MechanicObj)->GetBoolField(TEXT("breaks_on_attack"));

            ConfigAsset->StealthConfigurations.Add(LayerConfig);
        }
    }

    // STEP 6: FINALIZE DATA ASSET
    ConfigAsset->PostEditChange();
    Package->MarkPackageDirty();
    FAssetRegistryModule::AssetCreated(ConfigAsset);

    // STEP 7: SALVAMENTO OBRIGATÓRIO NO DISCO
    bool bSaved = UEditorAssetLibrary::SaveAsset(PackagePath, false);
    if (!bSaved)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateStealthConfigurationAsset: Failed to save data asset to disk: %s"), *PackagePath);
        return nullptr;
    }

    // STEP 8: VALIDATION - VERIFY FILE WAS CREATED
    bool bAssetExists = UEditorAssetLibrary::DoesAssetExist(PackagePath);
    if (!bAssetExists)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateStealthConfigurationAsset: Data asset was not created on disk: %s"), *PackagePath);
        return nullptr;
    }

    UE_LOG(LogTemp, Log, TEXT("CreateStealthConfigurationAsset: Successfully created and saved stealth config %s (Configurations: %d, Saved: %s)"),
           *SystemName, ConfigAsset->StealthConfigurations.Num(), bSaved ? TEXT("Yes") : TEXT("No"));

    return ConfigAsset;
}

UBlueprint* FUnrealMCPVisionCommands::CreateStealthBlueprint(const FString& SystemName, UStealthConfigurationDataAsset* ConfigAsset)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        UE_LOG(LogTemp, Error, TEXT("CreateStealthBlueprint: Must be called from game thread"));
        return nullptr;
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    if (SystemName.IsEmpty() || !ConfigAsset)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateStealthBlueprint: Invalid parameters"));
        return nullptr;
    }

    // STEP 3: CREATE PACKAGE FOR BLUEPRINT ASSET
    FString PackagePath = FString::Printf(TEXT("/Game/Auracron/Vision/StealthBlueprints/%s_Stealth"), *SystemName);
    UPackage* Package = CreatePackage(*PackagePath);
    if (!Package)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateStealthBlueprint: Failed to create package: %s"), *PackagePath);
        return nullptr;
    }

    // STEP 4: CREATE BLUEPRINT USING MODERN UE 5.6.1 APIs
    UBlueprint* StealthBlueprint = FKismetEditorUtilities::CreateBlueprint(
        AActor::StaticClass(),
        Package,
        FName(*FString::Printf(TEXT("%s_Stealth"), *SystemName)),
        BPTYPE_Normal,
        UBlueprint::StaticClass(),
        UBlueprintGeneratedClass::StaticClass(),
        FName("UnrealMCPStealth")
    );

    if (!StealthBlueprint)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateStealthBlueprint: Failed to create blueprint"));
        return nullptr;
    }

    // STEP 5: ADD CONFIG ASSET REFERENCE TO BLUEPRINT
    FEdGraphPinType ConfigPinType;
    ConfigPinType.PinCategory = UEdGraphSchema_K2::PC_Object;
    ConfigPinType.PinSubCategoryObject = UStealthConfigurationDataAsset::StaticClass();

    FBPVariableDescription ConfigVar;
    ConfigVar.VarName = FName(TEXT("StealthConfiguration"));
    ConfigVar.VarType = ConfigPinType;
    ConfigVar.PropertyFlags |= CPF_Edit | CPF_BlueprintVisible;
    ConfigVar.Category = FText::FromString(TEXT("Stealth System"));
    ConfigVar.FriendlyName = TEXT("Stealth Configuration");

    StealthBlueprint->NewVariables.Add(ConfigVar);

    // STEP 6: COMPILE AND FINALIZE BLUEPRINT
    FKismetEditorUtilities::CompileBlueprint(StealthBlueprint);
    StealthBlueprint->PostEditChange();
    Package->MarkPackageDirty();
    FAssetRegistryModule::AssetCreated(StealthBlueprint);

    // STEP 7: SALVAMENTO OBRIGATÓRIO NO DISCO
    bool bSaved = UEditorAssetLibrary::SaveAsset(PackagePath, false);
    if (!bSaved)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateStealthBlueprint: Failed to save blueprint to disk: %s"), *PackagePath);
        return nullptr;
    }

    // STEP 8: VALIDATION - VERIFY FILE WAS CREATED
    bool bAssetExists = UEditorAssetLibrary::DoesAssetExist(PackagePath);
    if (!bAssetExists)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateStealthBlueprint: Blueprint asset was not created on disk: %s"), *PackagePath);
        return nullptr;
    }

    UE_LOG(LogTemp, Log, TEXT("CreateStealthBlueprint: Successfully created and saved stealth blueprint %s (Variables: %d, Saved: %s)"),
           *SystemName, StealthBlueprint->NewVariables.Num(), bSaved ? TEXT("Yes") : TEXT("No"));

    return StealthBlueprint;
}
