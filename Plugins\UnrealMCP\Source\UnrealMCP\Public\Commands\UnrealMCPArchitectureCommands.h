#pragma once

#include "CoreMinimal.h"
#include "Json.h"

// Modern UE 5.6.1 Static Mesh APIs
#include "Engine/StaticMeshActor.h"
#include "Components/StaticMeshComponent.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Components/HierarchicalInstancedStaticMeshComponent.h"

// Modern UE 5.6.1 Spline APIs
#include "Components/SplineComponent.h"
#include "Components/SplineMeshComponent.h"

// Modern UE 5.6.1 Construction Script APIs
#include "Engine/SimpleConstructionScript.h"
#include "Components/ActorComponent.h"
#include "Components/ChildActorComponent.h"

// Experimental UE 5.6.1 PCG APIs - Forward declarations to avoid header issues
// MODERN UE 5.6.1 PCG INCLUDES - PRODUCTION READY
#include "PCGComponent.h"
#include "PCGGraph.h"
#include "PCGSubsystem.h"
#include "PCGData.h"
#include "PCGManagedResource.h"
namespace UE { namespace PCG { class FPCGContext; } }

// Forward declarations for MOBA map generation
class ALandscape;

// Engine APIs
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "GameFramework/Actor.h"
#include "EngineUtils.h"
#include "Materials/MaterialInterface.h"
#include "PhysicsEngine/BodySetup.h"

// Editor APIs
#include "Editor.h"
#include "EditorAssetLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"

#include "UnrealMCPArchitectureCommands.generated.h"

// ========================================
// MODERN UE 5.6.1 ARCHITECTURE STRUCTURES
// ========================================

/**
 * Auracron tower structure configuration
 */
USTRUCT(BlueprintType)
struct UNREALMCP_API FAuracronTowerConfig
{
    GENERATED_USTRUCT_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString TowerName;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 LayerIndex = 0;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 TeamIndex = 0;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FVector TowerLocation = FVector::ZeroVector;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FVector TowerScale = FVector(1.0f, 1.0f, 1.0f);

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FRotator TowerRotation = FRotator::ZeroRotator;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float TowerHeight = 500.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float TowerRadius = 100.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 TowerLevels = 3;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bUseHierarchicalInstancing = true;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bUsePCGGeneration = true;

    FAuracronTowerConfig()
    {
        TowerName = TEXT("AuracronTower");
        LayerIndex = 0;
        TeamIndex = 0;
        TowerLocation = FVector::ZeroVector;
        TowerScale = FVector(1.0f, 1.0f, 1.0f);
        TowerRotation = FRotator::ZeroRotator;
        TowerHeight = 500.0f;
        TowerRadius = 100.0f;
        TowerLevels = 3;
        bUseHierarchicalInstancing = true;
        bUsePCGGeneration = true;
    }
};

/**
 * Building element configuration
 */
USTRUCT(BlueprintType)
struct UNREALMCP_API FBuildingElementConfig
{
    GENERATED_USTRUCT_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString ElementName;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString ElementType; // wall, pillar, roof, foundation

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FVector ElementScale = FVector(1.0f, 1.0f, 1.0f);

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FTransform ElementTransform;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TSoftObjectPtr<UStaticMesh> ElementMesh;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TSoftObjectPtr<UMaterialInterface> ElementMaterial;

    FBuildingElementConfig()
    {
        ElementName = TEXT("BuildingElement");
        ElementType = TEXT("wall");
        ElementScale = FVector(1.0f, 1.0f, 1.0f);
        ElementTransform = FTransform::Identity;
        ElementMesh = nullptr;
        ElementMaterial = nullptr;
    }
};

/**
 * Spline-based structure configuration
 */
USTRUCT(BlueprintType)
struct UNREALMCP_API FSplineStructureConfig
{
    GENERATED_USTRUCT_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString StructureName;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TArray<FVector> SplinePoints;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float StructureWidth = 200.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float StructureHeight = 300.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 SplineSegments = 10;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bClosedLoop = false;

    FSplineStructureConfig()
    {
        StructureName = TEXT("SplineStructure");
        StructureWidth = 200.0f;
        StructureHeight = 300.0f;
        SplineSegments = 10;
        bClosedLoop = false;
    }
};

/**
 * Jungle Camp Configuration - Modern UE 5.6.1 PCG-based jungle generation
 */
USTRUCT(BlueprintType)
struct UNREALMCP_API FJungleCampConfig
{
    GENERATED_USTRUCT_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString CampName;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString CampType; // small, medium, epic, boss

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 LayerIndex;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FVector CampLocation;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float CampRadius = 400.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float VegetationDensity = 0.6f; // 0.0 to 1.0

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 TreeCount = 8;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 BushCount = 12;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 MonsterSpawnPoints = 1;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float RespawnTime = 120.0f; // seconds

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bUsePCGGeneration = true;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bCreateClearingCenter = false;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TArray<TSoftObjectPtr<UStaticMesh>> TreeMeshes;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TArray<TSoftObjectPtr<UStaticMesh>> BushMeshes;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TSoftObjectPtr<UMaterialInterface> LayerMaterial;

    FJungleCampConfig()
    {
        CampName = TEXT("JungleCamp");
        CampType = TEXT("small");
        LayerIndex = 0;
        CampLocation = FVector::ZeroVector;
        CampRadius = 400.0f;
        VegetationDensity = 0.6f;
        TreeCount = 8;
        BushCount = 12;
        MonsterSpawnPoints = 1;
        RespawnTime = 120.0f;
        bUsePCGGeneration = true;
        bCreateClearingCenter = false;
    }
};

/**
 * UnrealMCP Architecture Commands - Modern UE 5.6.1 Implementation
 * Handles procedural architectural structure creation with Auracron-specific designs
 */
UCLASS()
class UNREALMCP_API UUnrealMCPArchitectureCommands : public UObject
{
    GENERATED_BODY()

public:
    /**
     * Handle architecture command routing
     * @param CommandName Name of the command to execute
     * @param Params JSON parameters for the command
     * @return JSON response with command results
     */
    TSharedPtr<FJsonObject> HandleCommand(const FString& CommandName, const TSharedPtr<FJsonObject>& Params);

    /**
     * Create tower structures using modern UE 5.6.1 APIs
     * @param Params - Must include:
     *                "tower_name" - Name of the tower
     *                "tower_type" - Type of tower (basic, advanced, nexus)
     *                "location" - Tower location
     *                "layer_index" - Layer index (0=Planície, 1=Firmamento, 2=Abismo)
     *                "team_index" - Team index for styling
     * @return JSON response with the created tower details
     */
    TSharedPtr<FJsonObject> HandleCreateTowerStructures(const TSharedPtr<FJsonObject>& Params);

    /**
     * Create base buildings using hierarchical instancing
     * @param Params - Must include:
     *                "building_name" - Name of the building
     *                "building_type" - Type of building (barracks, shop, fountain)
     *                "location" - Building location
     *                "layer_index" - Layer index
     * @return JSON response with building creation results
     */
    TSharedPtr<FJsonObject> HandleCreateBaseBuildings(const TSharedPtr<FJsonObject>& Params);

    /**
     * Create inhibitor monuments with unique designs
     * @param Params - Must include:
     *                "monument_name" - Name of the monument
     *                "location" - Monument location
     *                "layer_index" - Layer index
     *                "team_index" - Team index
     * @return JSON response with monument creation results
     */
    TSharedPtr<FJsonObject> HandleCreateInhibitorMonuments(const TSharedPtr<FJsonObject>& Params);

    /**
     * Create nexus architecture with advanced geometry
     * @param Params - Must include:
     *                "nexus_name" - Name of the nexus
     *                "location" - Nexus location
     *                "team_index" - Team index
     *                "complexity" - Architecture complexity level
     * @return JSON response with nexus creation results
     */
    TSharedPtr<FJsonObject> HandleCreateNexusArchitecture(const TSharedPtr<FJsonObject>& Params);

    /**
     * Create defensive walls using spline-based generation
     * @param Params - Must include:
     *                "wall_name" - Name of the wall system
     *                "wall_points" - Array of wall control points
     *                "wall_height" - Height of the walls
     *                "layer_index" - Layer index
     * @return JSON response with wall creation results
     */
    TSharedPtr<FJsonObject> HandleCreateDefensiveWalls(const TSharedPtr<FJsonObject>& Params);

    /**
     * Create jungle camps with natural integration
     * @param Params - Must include:
     *                "camp_name" - Name of the jungle camp
     *                "location" - Camp location
     *                "camp_type" - Type of camp (neutral, epic, boss)
     *                "layer_index" - Layer index
     * @return JSON response with camp creation results
     */
    TSharedPtr<FJsonObject> HandleCreateJungleCamps(const TSharedPtr<FJsonObject>& Params);

    /**
     * CORREÇÃO CRÍTICA: Create real minion spawning system with Blueprints
     * @param Params - Must include:
     *                "minion_system_name" - Name of the minion system
     *                "minion_types" - Array of minion types to create
     *                "spawn_points" - Array of spawn locations
     * @return JSON response with created Blueprint assets
     */
    TSharedPtr<FJsonObject> HandleCreateMinionSpawningSystem(const TSharedPtr<FJsonObject>& Params);

    /**
     * CORREÇÃO CRÍTICA: Create real multilayer tower system with Blueprints
     * @param Params - Must include:
     *                "tower_system_name" - Name of the tower system
     *                "layer_configurations" - Array of layer configurations
     * @return JSON response with created Blueprint assets
     */
    TSharedPtr<FJsonObject> HandleCreateMultilayerTowerSystem(const TSharedPtr<FJsonObject>& Params);

private:
    /**
     * Create robust tower structure using modern UE 5.6.1 APIs
     * @param TowerConfig Tower configuration
     * @return Created tower actor
     */
    AActor* CreateRobustTowerStructure(const FAuracronTowerConfig& TowerConfig);

    /**
     * CORREÇÃO CRÍTICA: Create real Blueprint asset for towers
     * @param TowerConfig Tower configuration
     * @return Created Blueprint asset that can be used in the game
     */
    UBlueprint* CreateRealTowerBlueprint(const FAuracronTowerConfig& TowerConfig);

    /**
     * CORREÇÃO CRÍTICA: Create real Blueprint asset for minions
     * @param MinionName Name of the minion
     * @param MinionType Type of minion (Luz, Vento, Sombra)
     * @param LayerIndex Layer index
     * @return Created Blueprint asset that can be used in the game
     */
    UBlueprint* CreateRealMinionBlueprint(const FString& MinionName, const FString& MinionType, int32 LayerIndex);

    /**
     * Create minion spawner Blueprint
     * @param SystemName Name of the spawning system
     * @return Created spawner Blueprint
     */
    UBlueprint* CreateMinionSpawnerBlueprint(const FString& SystemName);

    /**
     * Create tower manager Blueprint
     * @param SystemName Name of the tower system
     * @return Created manager Blueprint
     */
    UBlueprint* CreateTowerManagerBlueprint(const FString& SystemName);

    /**
     * Create building using hierarchical instanced static mesh
     * @param BuildingName Name of the building
     * @param BuildingType Type of building
     * @param Location Building location
     * @param LayerIndex Layer index
     * @return Created building actor
     */
    AActor* CreateHierarchicalBuilding(const FString& BuildingName, const FString& BuildingType, const FVector& Location, int32 LayerIndex);

    /**
     * Create spline-based structure using modern spline APIs
     * @param SplineConfig Spline structure configuration
     * @return Created spline structure actor
     */
    AActor* CreateSplineBasedStructure(const FSplineStructureConfig& SplineConfig);

    /**
     * Create modular architecture using construction script
     * @param ArchitectureName Name of the architecture
     * @param Elements Array of building elements
     * @param Location Base location
     * @return Created modular architecture actor
     */
    AActor* CreateModularArchitecture(const FString& ArchitectureName, const TArray<FBuildingElementConfig>& Elements, const FVector& Location);

    /**
     * Setup PCG-based procedural generation using modern UE 5.6.1 APIs - PRODUCTION READY
     * @param StructureName Name of the structure
     * @param LayerIndex Layer index
     * @return PCG component for procedural generation
     */
    static UActorComponent* SetupPCGGeneration(const FString& StructureName, int32 LayerIndex);

    /**
     * MODERN UE 5.6.1 PCG CONFIGURATION FUNCTIONS - PRODUCTION READY
     */

    /**
     * Configure PCG for Planície Radiante (Golden Plains) - Layer 0
     * @param PCGGraph PCG Graph to configure
     * @param StructureName Name of the structure
     */
    static void ConfigurePCGForPlanicieRadiante(UPCGGraph* PCGGraph, const FString& StructureName);

    /**
     * Configure PCG for Firmamento Zephyr (Sky Realm) - Layer 1
     * @param PCGGraph PCG Graph to configure
     * @param StructureName Name of the structure
     */
    static void ConfigurePCGForFirmamentoZephyr(UPCGGraph* PCGGraph, const FString& StructureName);

    /**
     * Configure PCG for Abismo Umbral (Shadow Abyss) - Layer 2
     * @param PCGGraph PCG Graph to configure
     * @param StructureName Name of the structure
     */
    static void ConfigurePCGForAbismoUmbral(UPCGGraph* PCGGraph, const FString& StructureName);

    /**
     * Configure default PCG settings for unknown layers
     * @param PCGGraph PCG Graph to configure
     * @param StructureName Name of the structure
     */
    static void ConfigurePCGDefault(UPCGGraph* PCGGraph, const FString& StructureName);

    /**
     * Get layer-specific architectural style
     * @param LayerIndex Layer index
     * @return Architectural configuration for the layer
     */
    FAuracronTowerConfig GetLayerArchitecturalStyle(int32 LayerIndex);

    /**
     * STRATEGIC MOBA POSITIONING - Calculate optimal tower position based on MOBA design principles
     * @param RequestedLocation Original requested location
     * @param TowerType Type of tower (basic, advanced, nexus, inhibitor)
     * @param LayerIndex Layer index (0=Planície, 1=Firmamento, 2=Abismo)
     * @param TeamIndex Team index (0=Blue, 1=Red)
     * @return Strategically optimized position
     */
    FVector CalculateStrategicTowerPosition(const FVector& RequestedLocation, const FString& TowerType, int32 LayerIndex, int32 TeamIndex);

    /**
     * Calculate nearest lane position for strategic placement
     * @param Position Input position
     * @return Nearest lane center position
     */
    FVector CalculateNearestLanePosition(const FVector& Position);

    /**
     * Ensure minimum distance between towers for strategic balance
     * @param Position Proposed position
     * @param MinDistance Minimum required distance
     * @return Adjusted position maintaining minimum distance
     */
    FVector EnsureMinimumTowerDistance(const FVector& Position, float MinDistance);

    /**
     * Validate and adjust position for map bounds and conflicts
     * @param Position Proposed position
     * @param TowerType Type of tower for specific validation rules
     * @return Validated and adjusted position
     */
    FVector ValidateAndAdjustPosition(const FVector& Position, const FString& TowerType);

    /**
     * ADVANCED WALL SYSTEM - Get or create wall mesh for spline-based walls
     * @param SplineConfig Configuration for the wall structure
     * @return Static mesh for wall segments
     */
    UStaticMesh* GetOrCreateWallMesh(const FSplineStructureConfig& SplineConfig);

    /**
     * Create defensive features along wall segments (spikes, merlons, etc.)
     * @param WallActor The wall actor to add features to
     * @param SplineComponent The spline component defining the wall path
     * @param SegmentIndex Index of the segment to add features to
     * @param SplineConfig Configuration for the wall structure
     */
    void CreateDefensiveWallFeatures(AActor* WallActor, USplineComponent* SplineComponent, int32 SegmentIndex, const FSplineStructureConfig& SplineConfig);

    /**
     * Create corner towers at significant direction changes in wall splines
     * @param WallActor The wall actor to add towers to
     * @param SplineComponent The spline component defining the wall path
     * @param SplineConfig Configuration for the wall structure
     */
    void CreateCornerTowers(AActor* WallActor, USplineComponent* SplineComponent, const FSplineStructureConfig& SplineConfig);

    /**
     * ADVANCED JUNGLE SYSTEM - Create jungle camp with PCG-generated vegetation
     * @param CampConfig Configuration for the jungle camp
     * @return Created jungle camp actor with vegetation
     */
    AActor* CreateAdvancedJungleCamp(const FJungleCampConfig& CampConfig);

    /**
     * Apply layer-specific theming to jungle camp (Planície/Firmamento/Abismo)
     * @param CampConfig Configuration to modify with layer theming
     * @param LayerIndex Layer index (0=Planície, 1=Firmamento, 2=Abismo)
     */
    void ApplyLayerThemeToJungleCamp(FJungleCampConfig& CampConfig, int32 LayerIndex);

    /**
     * Create PCG-based vegetation using modern UE 5.6.1 APIs
     * @param CampActor The camp actor to add vegetation to
     * @param CampConfig Configuration for vegetation generation
     */
    void CreatePCGVegetation(AActor* CampActor, const FJungleCampConfig& CampConfig);

    /**
     * Create monster spawn points within jungle camp
     * @param CampActor The camp actor to add spawn points to
     * @param CampConfig Configuration for spawn points
     */
    void CreateMonsterSpawnPoints(AActor* CampActor, const FJungleCampConfig& CampConfig);

    /**
     * Create hierarchical instanced vegetation (fallback when PCG is not available)
     * @param CampActor The camp actor to add vegetation to
     * @param CampConfig Configuration for vegetation generation
     */
    void CreateHierarchicalInstancedVegetation(AActor* CampActor, const FJungleCampConfig& CampConfig);

    /**
     * Create camp clearing for boss fights
     * @param CampActor The camp actor to add clearing to
     * @param CampConfig Configuration for the clearing
     */
    void CreateCampClearing(AActor* CampActor, const FJungleCampConfig& CampConfig);

    /**
     * CRITICAL WORLD PARTITION SAFETY - Validate World Partition state to prevent crashes
     * @param World World to validate
     * @return True if safe to proceed with World Partition operations
     */
    bool ValidateWorldPartitionSafety(UWorld* World);

    /**
     * SAFE DATA LAYER CREATION - Create Data Layer with comprehensive safety validation
     * @param LayerName Name of the Data Layer to create
     * @param LayerIndex Layer index (0=Planície, 1=Firmamento, 2=Abismo)
     * @return Created Data Layer instance or nullptr if failed
     */
    UDataLayerInstance* CreateSafeDataLayer(const FString& LayerName, int32 LayerIndex);

    /**
     * SAFE ACTOR ASSIGNMENT - Assign actor to Data Layer with crash prevention
     * @param Actor Actor to assign
     * @param DataLayer Data Layer to assign to
     * @return True if assignment succeeded
     */
    bool AssignActorToDataLayerSafely(AActor* Actor, UDataLayerInstance* DataLayer);

    /**
     * ORGANIZE ALL ELEMENTS - Organize all Auracron elements into appropriate Data Layers
     * Uses height-based and name-based assignment with comprehensive safety validation
     */
    void OrganizeAllElementsIntoDataLayers();

    /**
     * CREATE COMPLETE MOBA MAP - Generate entire MOBA map like League of Legends/Dota 2
     * Uses MODERN UE 5.6.1 PCG Framework APIs for terrain, lanes, jungle, river, structures
     */
    void CreateCompleteMOBAMap();

    /**
     * CREATE PROCEDURAL TERRAIN - Generate base terrain using Landscape + PCG integration
     * @param World World context for terrain creation
     */
    void CreateProceduralTerrain(UWorld* World);

    /**
     * CREATE MOBA LANE SYSTEM - Generate 3-lane system (Top, Mid, Bot) using splines
     * @param World World context for lane creation
     */
    void CreateMOBALaneSystem(UWorld* World);

    /**
     * CREATE ROBUST PCG GRAPH - Create and save PCG Graph asset to disk
     * @param GraphName Name of the graph to create
     * @param World World context
     * @return Created PCG Graph or nullptr if failed
     */
    UPCGGraph* CreateRobustPCGGraph(const FString& GraphName, UWorld* World);

    /**
     * CREATE TERRAIN GENERATION NODES - Create and connect terrain PCG nodes
     * @param Graph PCG Graph to add nodes to
     * @param Landscape Landscape actor for integration
     */
    void CreateTerrainGenerationNodes(UPCGGraph* Graph, ALandscape* Landscape);

    /**
     * CREATE SINGLE LANE - Create individual lane with spline system
     * @param Graph PCG Graph to add lane to
     * @param LaneName Name of the lane
     * @param StartPoint Lane start position
     * @param EndPoint Lane end position
     * @param World World context
     */
    void CreateSingleLane(UPCGGraph* Graph, const FString& LaneName, const FVector& StartPoint, const FVector& EndPoint, UWorld* World);

    /**
     * CREATE COMPLETE JUNGLE SYSTEM - Generate jungle with camps and vegetation
     * @param World World context for jungle creation
     */
    void CreateCompleteJungleSystem(UWorld* World);

    /**
     * CREATE RIVER SYSTEM - Generate river dividing the map
     * @param World World context for river creation
     */
    void CreateRiverSystem(UWorld* World);

    /**
     * CREATE MOBA STRUCTURES - Generate towers, bases, etc.
     * @param World World context for structure creation
     */
    void CreateMOBAStructures(UWorld* World);

    /**
     * EXECUTE AND SAVE ALL PCG GRAPHS - Execute all graphs and save results
     * @param World World context for execution
     */
    void ExecuteAndSaveAllPCGGraphs(UWorld* World);

    /**
     * CREATE REAL SPLINE ACTORS FOR LANES - Create actual spline actors in world
     * @param World World context
     * @param LaneNames Names of lanes to create
     * @param StartPoints Start positions for each lane
     * @param EndPoints End positions for each lane
     */
    void CreateRealSplineActorsForLanes(UWorld* World, const TArray<FString>& LaneNames, const TArray<FVector>& StartPoints, const TArray<FVector>& EndPoints);

    /**
     * CREATE REAL JUNGLE CAMPS - Create actual jungle camp actors like League of Legends
     * @param World World context for camp creation
     */
    void CreateRealJungleCamps(UWorld* World);

    /**
     * CREATE REAL VEGETATION ACTORS - Create actual vegetation actors in jungle areas
     * @param World World context for vegetation creation
     */
    void CreateRealVegetationActors(UWorld* World);

    /**
     * VALIDATE PCG EXECUTION - Validate that PCG graphs generated real content in world
     * @param World World context for validation
     */
    void ValidatePCGExecution(UWorld* World);

private:
    // Cache for created structures
    UPROPERTY()
    TMap<FString, TObjectPtr<AActor>> CreatedStructures;

    // Cache for hierarchical instanced components
    UPROPERTY()
    TMap<FString, TObjectPtr<UHierarchicalInstancedStaticMeshComponent>> HierarchicalComponents;

    // Cache for spline components
    UPROPERTY()
    TMap<FString, TObjectPtr<USplineComponent>> SplineComponents;

    // Cache for construction scripts
    UPROPERTY()
    TMap<FString, TObjectPtr<USimpleConstructionScript>> ConstructionScripts;

    /**
     * BIOME CORE V2 COMPLETE IMPLEMENTATION - Create complete biome system with multiple biomes, blending, cache local, subgraphs
     * @param World World context for biome creation
     * @param Landscape Landscape actor for biome integration
     */
    void CreateBiomeCoreV2System(UWorld* World, ALandscape* Landscape);

    /**
     * CREATE BIOME SUBGRAPHS - Create reusable biome subgraphs (Biome Core V2 feature)
     * @param World World context for subgraph creation
     */
    void CreateBiomeSubgraphs(UWorld* World);

    /**
     * CREATE BIOME BLENDING SYSTEM - Create biome blending system (Biome Core V2 feature)
     * @param World World context for blending system
     * @param Landscape Landscape actor for blending integration
     */
    void CreateBiomeBlendingSystem(UWorld* World, ALandscape* Landscape);

    /**
     * CREATE BIOME CACHE SYSTEM - Create cache local per biome actor (Biome Core V2 feature)
     * @param World World context for cache system
     */
    void CreateBiomeCacheSystem(UWorld* World);

    /**
     * INTEGRATE BIOMES WITH PCG - Integrate all biomes with main PCG system (Biome Core V2 feature)
     * @param World World context for integration
     * @param Landscape Landscape actor for integration
     */
    void IntegrateBiomesWithPCG(UWorld* World, ALandscape* Landscape);

    /**
     * PCG PROCESSAMENTO GPU COMPLETE IMPLEMENTATION - Create complete GPU processing system with compute shaders, data interfaces, kernels
     * @param World World context for GPU processing
     * @param Landscape Landscape actor for GPU integration
     */
    void CreatePCGGPUProcessing(UWorld* World, ALandscape* Landscape);

    /**
     * CREATE GPU COMPUTE GRAPH - Create GPU compute graph for terrain processing (REAL UE 5.6.1 API)
     * @param World World context for compute graph
     * @param Landscape Landscape actor for compute integration
     */
    void CreateGPUComputeGraph(UWorld* World, ALandscape* Landscape);

    /**
     * CREATE VIRTUAL TEXTURE GPU INTERFACE - Create Virtual Texture GPU data interface (REAL UE 5.6.1 API)
     * @param World World context for VT interface
     * @param Landscape Landscape actor for VT integration
     */
    void CreateVirtualTextureGPUInterface(UWorld* World, ALandscape* Landscape);

    /**
     * CREATE STATIC MESH SPAWNER GPU KERNEL - Create Static Mesh Spawner GPU kernel (REAL UE 5.6.1 API)
     * @param World World context for mesh spawner kernel
     */
    void CreateStaticMeshSpawnerGPUKernel(UWorld* World);

    /**
     * CREATE CUSTOM HLSL KERNELS - Create Custom HLSL kernels for advanced processing (REAL UE 5.6.1 API)
     * @param World World context for HLSL kernels
     */
    void CreateCustomHLSLKernels(UWorld* World);

    /**
     * INTEGRATE GPU PROCESSING WITH PCG - Integrate GPU processing with main PCG system (REAL UE 5.6.1 API)
     * @param World World context for GPU integration
     * @param Landscape Landscape actor for GPU integration
     */
    void IntegrateGPUProcessingWithPCG(UWorld* World, ALandscape* Landscape);

    /**
     * PCG SPAWNING MODERNO COMPLETE IMPLEMENTATION - Create complete modern spawning system with frustum culling, streaming, HISM
     * @param World World context for modern spawning
     * @param Landscape Landscape actor for spawning integration
     */
    void CreateModernPCGSpawning(UWorld* World, ALandscape* Landscape);

    /**
     * CREATE MODERN STATIC MESH SPAWNING - Create modern static mesh spawning with HISM optimization (REAL UE 5.6.1 API)
     * @param World World context for mesh spawning
     * @param Landscape Landscape actor for spawning integration
     */
    void CreateModernStaticMeshSpawning(UWorld* World, ALandscape* Landscape);

    /**
     * CREATE FRUSTUM CULLING INTEGRATED - Create frustum culling integrated system (REAL UE 5.6.1 API)
     * @param World World context for frustum culling
     */
    void CreateFrustumCullingIntegrated(UWorld* World);

    /**
     * CREATE STREAMING DE WORLD - Create streaming de mundo with World Partition (REAL UE 5.6.1 API)
     * @param World World context for streaming
     * @param Landscape Landscape actor for streaming integration
     */
    void CreateStreamingDeWorld(UWorld* World, ALandscape* Landscape);

    /**
     * CREATE RUNTIME GENERATION SYSTEM - Create runtime generation system (REAL UE 5.6.1 API)
     * @param World World context for runtime generation
     */
    void CreateRuntimeGenerationSystem(UWorld* World);

    /**
     * INTEGRATE MODERN SPAWNING SYSTEMS - Integrate all modern spawning systems (REAL UE 5.6.1 API)
     * @param World World context for spawning integration
     * @param Landscape Landscape actor for spawning integration
     */
    void IntegrateModernSpawningSystems(UWorld* World, ALandscape* Landscape);

    /**
     * PCG PERFORMANCE CONFIG COMPLETE IMPLEMENTATION - Create complete performance configuration with all optimizations
     * @param World World context for performance config
     */
    void CreatePCGPerformanceConfig(UWorld* World);

    /**
     * CONFIGURE MULTITHREADING AND EXECUTION - Configure multithreading and execution (REAL UE 5.6.1 API)
     */
    void ConfigureMultithreadingAndExecution();

    /**
     * CONFIGURE CACHE SYSTEM WITH CRC - Configure cache system with CRC (REAL UE 5.6.1 API)
     */
    void ConfigureCacheSystemWithCRC();

    /**
     * CONFIGURE RUNTIME GENERATION BUDGETS - Configure runtime generation budgets (REAL UE 5.6.1 API)
     */
    void ConfigureRuntimeGenerationBudgets();

    /**
     * CONFIGURE PERFORMANCE MONITORING - Configure performance monitoring (REAL UE 5.6.1 API)
     */
    void ConfigurePerformanceMonitoring();

    /**
     * CONFIGURE ADVANCED OPTIMIZATIONS - Configure advanced optimizations (REAL UE 5.6.1 API)
     */
    void ConfigureAdvancedOptimizations();

    /**
     * ROBUST UTILITY: Generate unique actor name to prevent "Cannot generate unique name" fatal errors
     * @param World The world to check for existing actors
     * @param BaseName The base name to make unique
     * @param MaxAttempts Maximum attempts to generate unique name (default: 1000)
     * @return Unique name that doesn't conflict with existing actors
     */
    static FString GenerateUniqueActorName(UWorld* World, const FString& BaseName, int32 MaxAttempts = 1000);
};
