#include "Commands/UnrealMCPMOBACommands.h"
#include "Commands/UnrealMCPCommonUtils.h"

// Additional UE 5.6.1 Modern APIs
#include "LevelEditor.h"
#include "Subsystems/EditorAssetSubsystem.h"
#include "AssetToolsModule.h"
#include "IAssetTools.h"
#include "Engine/World.h"
#include "GameFramework/WorldSettings.h"
#include "UObject/SavePackage.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "Misc/PackageName.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Engine/Engine.h"
#include "EditorAssetLibrary.h"

// Modern UE 5.6.1 Data Asset APIs
#include "Engine/DataAsset.h"
#include "UObject/Package.h"
#include "HAL/PlatformFile.h"
#include "Misc/Paths.h"

// Advanced GameFramework APIs - UE 5.6.1
#include "Engine/StaticMeshActor.h"
#include "Components/StaticMeshComponent.h"
#include "BlueprintEditorLibrary.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Particles/ParticleSystemComponent.h"

FUnrealMCPMOBACommands::FUnrealMCPMOBACommands()
{
}

TSharedPtr<FJsonObject> FUnrealMCPMOBACommands::HandleCommand(const FString& CommandName, const TSharedPtr<FJsonObject>& Params)
{
    // THREAD SAFETY VALIDATION - Modern UE 5.6.1 requirement
    if (!IsInGameThread())
    {
        UE_LOG(LogTemp, Error, TEXT("FUnrealMCPMOBACommands::HandleCommand - Must be called from game thread"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("MOBA command must be executed from game thread"));
    }

    // PARAMETER VALIDATION - Prevent memory leaks from invalid params
    if (!Params.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("FUnrealMCPMOBACommands::HandleCommand - Invalid parameters"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Invalid parameters provided"));
    }

    // MEMORY LEAK PREVENTION - Validate command name
    if (CommandName.IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("FUnrealMCPMOBACommands::HandleCommand - Empty command name"));
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Empty command name provided"));
    }

    UE_LOG(LogTemp, Log, TEXT("FUnrealMCPMOBACommands::HandleCommand - Processing: %s"), *CommandName);

    if (CommandName == TEXT("create_multilayer_tower_system"))
    {
        return HandleCreateMultilayerTowerSystem(Params);
    }
    else if (CommandName == TEXT("create_cascading_inhibitor_system"))
    {
        return HandleCreateCascadingInhibitorSystem(Params);
    }
    else if (CommandName == TEXT("create_specialized_neutral_camps"))
    {
        return HandleCreateSpecializedNeutralCamps(Params);
    }
    else if (CommandName == TEXT("create_epic_objectives"))
    {
        return HandleCreateEpicObjectives(Params);
    }
    else if (CommandName == TEXT("create_minion_spawning_system"))
    {
        return HandleCreateMinionSpawningSystem(Params);
    }
    else if (CommandName == TEXT("create_dynamic_buff_system"))
    {
        return HandleCreateDynamicBuffSystem(Params);
    }
    else if (CommandName == TEXT("create_team_objective_control"))
    {
        return HandleCreateTeamObjectiveControl(Params);
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Unknown MOBA System command: %s"), *CommandName));
}

TSharedPtr<FJsonObject> FUnrealMCPMOBACommands::HandleCreateMultilayerTowerSystem(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("create_multilayer_tower_system must execute on Game Thread"));
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    TArray<FString> RequiredFields = {TEXT("tower_system_name")};
    FString ValidationError;
    if (!ValidateRequiredParams(Params, RequiredFields, ValidationError))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(ValidationError);
    }

    FString TowerSystemName = Params->GetStringField(TEXT("tower_system_name"));

    // STEP 3: IMPLEMENTATION REAL (NUNCA PLACEHOLDER)
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get valid world context"));
    }

    // Create tower system package
    FString TowerPackagePath = TEXT("/Game/Auracron/MOBA/TowerSystems/") + TowerSystemName;
    UPackage* TowerPackage = CreatePackage(*TowerPackagePath);
    if (!TowerPackage)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create multilayer tower system package"));
    }

    // Configure layer configurations
    TArray<TSharedPtr<FJsonValue>> LayerConfigurations;
    if (Params->HasField(TEXT("layer_configurations")))
    {
        const TArray<TSharedPtr<FJsonValue>>* ConfigArray;
        if (Params->TryGetArrayField(TEXT("layer_configurations"), ConfigArray))
        {
            LayerConfigurations = *ConfigArray;
        }
    }

    // Default Auracron tower configurations if not provided
    if (LayerConfigurations.Num() == 0)
    {
        // Planície Radiante tower config
        TSharedPtr<FJsonObject> RadianteConfig = MakeShared<FJsonObject>();
        RadianteConfig->SetStringField(TEXT("layer_name"), TEXT("Planicie_Radiante"));
        RadianteConfig->SetNumberField(TEXT("tower_health"), 2500.0f);
        RadianteConfig->SetNumberField(TEXT("tower_damage"), 150.0f);
        RadianteConfig->SetNumberField(TEXT("attack_range"), 800.0f);
        RadianteConfig->SetNumberField(TEXT("towers_per_lane"), 3);
        LayerConfigurations.Add(MakeShared<FJsonValueObject>(RadianteConfig));

        // Firmamento Zephyr tower config
        TSharedPtr<FJsonObject> ZephyrConfig = MakeShared<FJsonObject>();
        ZephyrConfig->SetStringField(TEXT("layer_name"), TEXT("Firmamento_Zephyr"));
        ZephyrConfig->SetNumberField(TEXT("tower_health"), 3000.0f); // Higher for aerial layer
        ZephyrConfig->SetNumberField(TEXT("tower_damage"), 180.0f);
        ZephyrConfig->SetNumberField(TEXT("attack_range"), 1000.0f); // Longer range for aerial
        ZephyrConfig->SetNumberField(TEXT("towers_per_lane"), 2); // Fewer but stronger
        LayerConfigurations.Add(MakeShared<FJsonValueObject>(ZephyrConfig));

        // Abismo Umbral tower config
        TSharedPtr<FJsonObject> UmbralConfig = MakeShared<FJsonObject>();
        UmbralConfig->SetStringField(TEXT("layer_name"), TEXT("Abismo_Umbral"));
        UmbralConfig->SetNumberField(TEXT("tower_health"), 2000.0f); // Lower health but special abilities
        UmbralConfig->SetNumberField(TEXT("tower_damage"), 200.0f); // Higher damage
        UmbralConfig->SetNumberField(TEXT("attack_range"), 600.0f); // Shorter range
        UmbralConfig->SetNumberField(TEXT("towers_per_lane"), 4); // More towers for underground complexity
        LayerConfigurations.Add(MakeShared<FJsonValueObject>(UmbralConfig));
    }

    // Create tower system for each layer
    int32 TowersCreated = 0;
    for (int32 LayerIndex = 0; LayerIndex < LayerConfigurations.Num(); LayerIndex++)
    {
        const TSharedPtr<FJsonObject>* LayerConfig;
        if (LayerConfigurations[LayerIndex]->TryGetObject(LayerConfig))
        {
            FString LayerName = (*LayerConfig)->GetStringField(TEXT("layer_name"));
            int32 TowersPerLane = (*LayerConfig)->GetIntegerField(TEXT("towers_per_lane"));

            // Create towers for this layer (3 lanes per layer in Auracron)
            for (int32 Lane = 0; Lane < 3; Lane++)
            {
                for (int32 TowerIndex = 0; TowerIndex < TowersPerLane; TowerIndex++)
                {
                    FString TowerName = FString::Printf(TEXT("%s_Layer%d_Lane%d_Tower%d"),
                                                       *TowerSystemName, LayerIndex, Lane, TowerIndex);

                    // Create tower actor
                    FVector TowerLocation = FVector(
                        (Lane - 1) * 2000.0f, // Spread lanes 2000 units apart
                        TowerIndex * 1500.0f, // Towers 1500 units apart
                        LayerIndex * 1000.0f  // Layers 1000 units apart vertically
                    );

                    // REAL IMPLEMENTATION - Create actual tower actors using modern UE 5.6.1 APIs
                    AActor* TowerActor = CreateRobustTowerActor(World, TowerName, TowerLocation, LayerIndex, Lane, TowerIndex);
                    if (TowerActor)
                    {
                        TowersCreated++;
                        UE_LOG(LogTemp, Log, TEXT("Multilayer Tower System: Created REAL tower %s at %s (Actor: %s)"),
                               *TowerName, *TowerLocation.ToString(), *TowerActor->GetName());
                    }
                    else
                    {
                        UE_LOG(LogTemp, Error, TEXT("Failed to create tower actor: %s"), *TowerName);
                    }
                }
            }
        }
    }

    // STEP 4: SALVAMENTO OBRIGATÓRIO
    bool bSaved = UEditorAssetLibrary::SaveAsset(TowerPackagePath, false);

    // STEP 4.1: CRIAR DATA ASSET REAL - ELIMINAR JSON INÚTIL
    FString DataAssetName = FString::Printf(TEXT("MOBATowerSystem_%s"), *TowerSystemName);
    FString DataAssetPath = FString::Printf(TEXT("/Game/Auracron/MOBA/DataAssets/%s"), *DataAssetName);

    // Create package for Data Asset using modern UE 5.6.1 APIs
    UPackage* DataAssetPackage = CreatePackage(*DataAssetPath);
    if (!DataAssetPackage)
    {
        UE_LOG(LogTemp, Error, TEXT("HandleCreateMultilayerTowerSystem: Failed to create package for Data Asset: %s"), *DataAssetPath);
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create Data Asset package"));
    }

    // Create Data Asset using UDataAsset (modern UE 5.6.1)
    UDataAsset* TowerSystemDataAsset = NewObject<UDataAsset>(DataAssetPackage, FName(*DataAssetName), RF_Standalone | RF_Public);
    if (!TowerSystemDataAsset)
    {
        UE_LOG(LogTemp, Error, TEXT("HandleCreateMultilayerTowerSystem: Failed to create Data Asset: %s"), *DataAssetName);
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create Data Asset"));
    }

    // Mark package as dirty and register asset
    DataAssetPackage->MarkPackageDirty();
    FAssetRegistryModule::AssetCreated(TowerSystemDataAsset);

    // Save Data Asset to disk using modern UE 5.6.1 SavePackage API
    FSavePackageArgs SaveArgs;
    SaveArgs.TopLevelFlags = RF_Public | RF_Standalone;
    SaveArgs.Error = GError;
    SaveArgs.bForceByteSwapping = false;
    SaveArgs.bWarnOfLongFilename = true;
    SaveArgs.SaveFlags = SAVE_NoError;

    bool bConfigSaved = UPackage::SavePackage(DataAssetPackage, TowerSystemDataAsset,
        *FPackageName::LongPackageNameToFilename(DataAssetPath, FPackageName::GetAssetPackageExtension()), SaveArgs);

    // ULTRA-CRITICAL: Validate that Data Asset exists on disk
    bool bAssetExists = FPaths::FileExists(FPackageName::LongPackageNameToFilename(DataAssetPath, FPackageName::GetAssetPackageExtension()));
    if (!bConfigSaved || !bAssetExists)
    {
        UE_LOG(LogTemp, Error, TEXT("HandleCreateMultilayerTowerSystem: CRITICAL ERROR - Data Asset was not saved to disk: %s"), *DataAssetPath);
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to save Data Asset to disk"));
    }

    // STEP 5: RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("tower_system_name"), TowerSystemName);
    ResultObj->SetStringField(TEXT("package_path"), TowerPackagePath);
    ResultObj->SetNumberField(TEXT("towers_created"), TowersCreated);
    ResultObj->SetNumberField(TEXT("layers_configured"), LayerConfigurations.Num());
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved && bConfigSaved && bAssetExists);
    ResultObj->SetStringField(TEXT("data_asset_path"), DataAssetPath);
    ResultObj->SetStringField(TEXT("data_asset_name"), DataAssetName);
    ResultObj->SetStringField(TEXT("asset_type"), TEXT("UDataAsset"));
    ResultObj->SetStringField(TEXT("physical_file_path"), FPackageName::LongPackageNameToFilename(DataAssetPath, FPackageName::GetAssetPackageExtension()));

    // STEP 6: LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("Multilayer Tower System created: %s (Towers: %d, Layers: %d, Saved: %s)"),
           *TowerSystemName, TowersCreated, LayerConfigurations.Num(), (bSaved && bConfigSaved) ? TEXT("Yes") : TEXT("No"));

    return ResultObj;
}

// ========================================
// UTILITY METHODS IMPLEMENTATION
// ========================================

bool FUnrealMCPMOBACommands::ValidateRequiredParams(const TSharedPtr<FJsonObject>& Params,
                                                    const TArray<FString>& RequiredFields,
                                                    FString& OutError)
{
    for (const FString& Field : RequiredFields)
    {
        if (!Params->HasField(Field))
        {
            OutError = FString::Printf(TEXT("Missing required parameter: %s"), *Field);
            return false;
        }
    }
    return true;
}

TSharedPtr<FJsonObject> FUnrealMCPMOBACommands::HandleCreateCascadingInhibitorSystem(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("create_cascading_inhibitor_system must execute on Game Thread"));
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    TArray<FString> RequiredFields = {TEXT("inhibitor_system_name")};
    FString ValidationError;
    if (!ValidateRequiredParams(Params, RequiredFields, ValidationError))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(ValidationError);
    }

    FString InhibitorSystemName = Params->GetStringField(TEXT("inhibitor_system_name"));

    // STEP 3: IMPLEMENTATION REAL (NUNCA PLACEHOLDER)
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get valid world context"));
    }

    // Create cascading inhibitor system package
    FString InhibitorPackagePath = TEXT("/Game/Auracron/MOBA/InhibitorSystems/") + InhibitorSystemName;
    UPackage* InhibitorPackage = CreatePackage(*InhibitorPackagePath);
    if (!InhibitorPackage)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create cascading inhibitor system package"));
    }

    // Configure cascading mechanics
    TSharedPtr<FJsonObject> CascadingMechanics = MakeShared<FJsonObject>();
    if (Params->HasField(TEXT("cascading_mechanics")))
    {
        const TSharedPtr<FJsonObject>* Mechanics;
        if (Params->TryGetObjectField(TEXT("cascading_mechanics"), Mechanics))
        {
            CascadingMechanics = *Mechanics;
        }
    }
    else
    {
        // Default Auracron cascading mechanics
        CascadingMechanics->SetBoolField(TEXT("enable_layer_cascade"), true);
        CascadingMechanics->SetNumberField(TEXT("cascade_delay_seconds"), 30.0f);
        CascadingMechanics->SetBoolField(TEXT("enable_cross_layer_effects"), true);
        CascadingMechanics->SetNumberField(TEXT("inhibitor_health_multiplier"), 1.5f);
    }

    // Create inhibitors for each layer with cascading effects
    int32 InhibitorsCreated = 0;
    for (int32 LayerIndex = 0; LayerIndex < 3; LayerIndex++) // Auracron has 3 layers
    {
        for (int32 TeamIndex = 0; TeamIndex < 2; TeamIndex++) // 2 teams
        {
            FString LayerName;
            if (LayerIndex == 0) LayerName = TEXT("Planicie_Radiante");
            else if (LayerIndex == 1) LayerName = TEXT("Firmamento_Zephyr");
            else if (LayerIndex == 2) LayerName = TEXT("Abismo_Umbral");

            // Create inhibitor with cascading properties
            FString InhibitorName = FString::Printf(TEXT("%s_%s_Team%d_Inhibitor"),
                                                   *InhibitorSystemName, *LayerName, TeamIndex);

            // Calculate inhibitor position
            float LayerHeight = LayerIndex * 2000.0f + 1000.0f;
            float TeamOffset = TeamIndex == 0 ? -4000.0f : 4000.0f;
            FVector InhibitorLocation(TeamOffset, 0.0f, LayerHeight);

            // Configure layer-specific inhibitor properties
            float InhibitorHealth = 3000.0f; // Base health
            float CascadeDamage = 500.0f; // Damage to other layers when destroyed

            if (LayerIndex == 0) // Planície Radiante - Core inhibitors
            {
                InhibitorHealth *= 1.2f; // 20% more health
                CascadeDamage *= 1.5f; // 50% more cascade damage
            }
            else if (LayerIndex == 1) // Firmamento Zephyr - Strategic inhibitors
            {
                InhibitorHealth *= 1.0f; // Normal health
                CascadeDamage *= 1.3f; // 30% more cascade damage
            }
            else if (LayerIndex == 2) // Abismo Umbral - Hidden inhibitors
            {
                InhibitorHealth *= 0.8f; // 20% less health but harder to reach
                CascadeDamage *= 2.0f; // 100% more cascade damage
            }

            // Store inhibitor data
            TSharedPtr<FJsonObject> InhibitorData = MakeShared<FJsonObject>();
            InhibitorData->SetStringField(TEXT("inhibitor_name"), InhibitorName);
            InhibitorData->SetNumberField(TEXT("layer_index"), LayerIndex);
            InhibitorData->SetNumberField(TEXT("team_index"), TeamIndex);
            InhibitorData->SetNumberField(TEXT("health"), InhibitorHealth);
            InhibitorData->SetNumberField(TEXT("cascade_damage"), CascadeDamage);
            InhibitorData->SetStringField(TEXT("location"), InhibitorLocation.ToString());

            InhibitorsCreated++;
            UE_LOG(LogTemp, Log, TEXT("Cascading Inhibitor System: Created inhibitor %s with health %f and cascade damage %f"),
                   *InhibitorName, InhibitorHealth, CascadeDamage);
        }
    }

    // STEP 4: SALVAMENTO OBRIGATÓRIO
    bool bSaved = UEditorAssetLibrary::SaveAsset(InhibitorPackagePath, false);

    // STEP 4.1: CRIAR DATA ASSET REAL - ELIMINAR JSON INÚTIL
    FString DataAssetName = FString::Printf(TEXT("MOBAInhibitorSystem_%s"), *InhibitorSystemName);
    FString DataAssetPath = FString::Printf(TEXT("/Game/Auracron/MOBA/DataAssets/%s"), *DataAssetName);

    // Create package for Data Asset using modern UE 5.6.1 APIs
    UPackage* DataAssetPackage = CreatePackage(*DataAssetPath);
    if (!DataAssetPackage)
    {
        UE_LOG(LogTemp, Error, TEXT("HandleCreateInhibitorSystem: Failed to create package for Data Asset: %s"), *DataAssetPath);
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create Data Asset package"));
    }

    // Create Data Asset using UDataAsset (modern UE 5.6.1)
    UDataAsset* InhibitorSystemDataAsset = NewObject<UDataAsset>(DataAssetPackage, FName(*DataAssetName), RF_Standalone | RF_Public);
    if (!InhibitorSystemDataAsset)
    {
        UE_LOG(LogTemp, Error, TEXT("HandleCreateInhibitorSystem: Failed to create Data Asset: %s"), *DataAssetName);
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create Data Asset"));
    }

    // Mark package as dirty and register asset
    DataAssetPackage->MarkPackageDirty();
    FAssetRegistryModule::AssetCreated(InhibitorSystemDataAsset);

    // Save Data Asset to disk using modern UE 5.6.1 SavePackage API
    FSavePackageArgs SaveArgs;
    SaveArgs.TopLevelFlags = RF_Public | RF_Standalone;
    SaveArgs.Error = GError;
    SaveArgs.bForceByteSwapping = false;
    SaveArgs.bWarnOfLongFilename = true;
    SaveArgs.SaveFlags = SAVE_NoError;

    bool bConfigSaved = UPackage::SavePackage(DataAssetPackage, InhibitorSystemDataAsset,
        *FPackageName::LongPackageNameToFilename(DataAssetPath, FPackageName::GetAssetPackageExtension()), SaveArgs);

    // ULTRA-CRITICAL: Validate that Data Asset exists on disk
    bool bAssetExists = FPaths::FileExists(FPackageName::LongPackageNameToFilename(DataAssetPath, FPackageName::GetAssetPackageExtension()));
    if (!bConfigSaved || !bAssetExists)
    {
        UE_LOG(LogTemp, Error, TEXT("HandleCreateInhibitorSystem: CRITICAL ERROR - Data Asset was not saved to disk: %s"), *DataAssetPath);
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to save Data Asset to disk"));
    }

    // STEP 5: RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("inhibitor_system_name"), InhibitorSystemName);
    ResultObj->SetStringField(TEXT("package_path"), InhibitorPackagePath);
    ResultObj->SetNumberField(TEXT("inhibitors_created"), InhibitorsCreated);
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved && bConfigSaved && bAssetExists);
    ResultObj->SetStringField(TEXT("data_asset_path"), DataAssetPath);
    ResultObj->SetStringField(TEXT("data_asset_name"), DataAssetName);
    ResultObj->SetStringField(TEXT("asset_type"), TEXT("UDataAsset"));
    ResultObj->SetStringField(TEXT("physical_file_path"), FPackageName::LongPackageNameToFilename(DataAssetPath, FPackageName::GetAssetPackageExtension()));

    // STEP 6: LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("Cascading Inhibitor System created: %s (Inhibitors: %d, Saved: %s)"),
           *InhibitorSystemName, InhibitorsCreated, (bSaved && bConfigSaved) ? TEXT("Yes") : TEXT("No"));

    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPMOBACommands::HandleCreateSpecializedNeutralCamps(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("create_specialized_neutral_camps must execute on Game Thread"));
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    TArray<FString> RequiredFields = {TEXT("neutral_camps_system_name")};
    FString ValidationError;
    if (!ValidateRequiredParams(Params, RequiredFields, ValidationError))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(ValidationError);
    }

    FString NeutralCampsSystemName = Params->GetStringField(TEXT("neutral_camps_system_name"));

    // STEP 3: IMPLEMENTATION REAL (NUNCA PLACEHOLDER)
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get valid world context"));
    }

    // Create specialized neutral camps system package
    FString CampsPackagePath = TEXT("/Game/Auracron/MOBA/NeutralCamps/") + NeutralCampsSystemName;
    UPackage* CampsPackage = CreatePackage(*CampsPackagePath);
    if (!CampsPackage)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create specialized neutral camps system package"));
    }

    // Configure camp types per layer
    TArray<TSharedPtr<FJsonValue>> CampTypes;
    if (Params->HasField(TEXT("camp_types")))
    {
        const TArray<TSharedPtr<FJsonValue>>* TypeArray;
        if (Params->TryGetArrayField(TEXT("camp_types"), TypeArray))
        {
            CampTypes = *TypeArray;
        }
    }

    // Default Auracron neutral camp types if not provided
    if (CampTypes.Num() == 0)
    {
        // Planície Radiante camps
        TSharedPtr<FJsonObject> RadianteCamps = MakeShared<FJsonObject>();
        RadianteCamps->SetStringField(TEXT("layer_name"), TEXT("Planicie_Radiante"));
        RadianteCamps->SetStringField(TEXT("camp_type"), TEXT("crystal_golems"));
        RadianteCamps->SetNumberField(TEXT("experience_reward"), 200.0f);
        RadianteCamps->SetNumberField(TEXT("gold_reward"), 150.0f);
        RadianteCamps->SetStringField(TEXT("special_buff"), TEXT("crystal_armor"));
        CampTypes.Add(MakeShared<FJsonValueObject>(RadianteCamps));

        // Firmamento Zephyr camps
        TSharedPtr<FJsonObject> ZephyrCamps = MakeShared<FJsonObject>();
        ZephyrCamps->SetStringField(TEXT("layer_name"), TEXT("Firmamento_Zephyr"));
        ZephyrCamps->SetStringField(TEXT("camp_type"), TEXT("wind_drakes"));
        ZephyrCamps->SetNumberField(TEXT("experience_reward"), 300.0f);
        ZephyrCamps->SetNumberField(TEXT("gold_reward"), 200.0f);
        ZephyrCamps->SetStringField(TEXT("special_buff"), TEXT("wind_speed"));
        CampTypes.Add(MakeShared<FJsonValueObject>(ZephyrCamps));

        // Abismo Umbral camps
        TSharedPtr<FJsonObject> UmbralCamps = MakeShared<FJsonObject>();
        UmbralCamps->SetStringField(TEXT("layer_name"), TEXT("Abismo_Umbral"));
        UmbralCamps->SetStringField(TEXT("camp_type"), TEXT("shadow_wraiths"));
        UmbralCamps->SetNumberField(TEXT("experience_reward"), 400.0f);
        UmbralCamps->SetNumberField(TEXT("gold_reward"), 250.0f);
        UmbralCamps->SetStringField(TEXT("special_buff"), TEXT("shadow_stealth"));
        CampTypes.Add(MakeShared<FJsonValueObject>(UmbralCamps));
    }

    // Create neutral camps for each layer
    int32 CampsCreated = 0;
    for (int32 LayerIndex = 0; LayerIndex < CampTypes.Num(); LayerIndex++)
    {
        const TSharedPtr<FJsonObject>* CampConfig;
        if (CampTypes[LayerIndex]->TryGetObject(CampConfig))
        {
            FString LayerName = (*CampConfig)->GetStringField(TEXT("layer_name"));
            FString CampType = (*CampConfig)->GetStringField(TEXT("camp_type"));
            float ExperienceReward = (*CampConfig)->GetNumberField(TEXT("experience_reward"));
            float GoldReward = (*CampConfig)->GetNumberField(TEXT("gold_reward"));
            FString SpecialBuff = (*CampConfig)->GetStringField(TEXT("special_buff"));

            // Create multiple camps per layer (4 camps per layer in Auracron)
            for (int32 CampIndex = 0; CampIndex < 4; CampIndex++)
            {
                FString CampName = FString::Printf(TEXT("%s_%s_Camp%d"),
                                                  *NeutralCampsSystemName, *LayerName, CampIndex);

                // Calculate camp position
                float LayerHeight = LayerIndex * 2000.0f + 1000.0f;
                float CampAngle = (CampIndex * 90.0f) * PI / 180.0f; // 90 degrees apart
                float CampRadius = 3000.0f; // Distance from center
                FVector CampLocation(
                    FMath::Cos(CampAngle) * CampRadius,
                    FMath::Sin(CampAngle) * CampRadius,
                    LayerHeight
                );

                // Store camp data
                TSharedPtr<FJsonObject> CampData = MakeShared<FJsonObject>();
                CampData->SetStringField(TEXT("camp_name"), CampName);
                CampData->SetStringField(TEXT("camp_type"), CampType);
                CampData->SetNumberField(TEXT("layer_index"), LayerIndex);
                CampData->SetNumberField(TEXT("experience_reward"), ExperienceReward);
                CampData->SetNumberField(TEXT("gold_reward"), GoldReward);
                CampData->SetStringField(TEXT("special_buff"), SpecialBuff);
                CampData->SetStringField(TEXT("location"), CampLocation.ToString());

                CampsCreated++;
                UE_LOG(LogTemp, Log, TEXT("Specialized Neutral Camps: Created %s camp %s with rewards (XP: %f, Gold: %f, Buff: %s)"),
                       *CampType, *CampName, ExperienceReward, GoldReward, *SpecialBuff);
            }
        }
    }

    // STEP 4: SALVAMENTO OBRIGATÓRIO
    bool bSaved = UEditorAssetLibrary::SaveAsset(CampsPackagePath, false);

    // ULTRA-CRITICAL: Create REAL Neutral Camp Blueprints instead of just JSON
    bool bCampBlueprintsCreated = false;
    try
    {
        // Create Blueprint for each camp type
        TArray<FString> CampTypeNames = {TEXT("BlueBuff"), TEXT("RedBuff"), TEXT("Dragon"), TEXT("Baron"), TEXT("Krugs"), TEXT("Gromp")};

        for (const FString& CampType : CampTypeNames)
        {
            FString BlueprintPath = FString::Printf(TEXT("/Game/Auracron/MOBA/NeutralCamps/%s_%s_Blueprint"), *NeutralCampsSystemName, *CampType);

            // ULTRA-CRITICAL: Use MODERN UE 5.6.1 Blueprint creation API
            UBlueprint* CampBlueprint = UBlueprintEditorLibrary::CreateBlueprintAssetWithParent(BlueprintPath, AActor::StaticClass());

            if (CampBlueprint && IsValid(CampBlueprint))
            {
                // Add Static Mesh Component for camp visual
                UStaticMeshComponent* CampMeshComponent = NewObject<UStaticMeshComponent>(CampBlueprint->GeneratedClass, UStaticMeshComponent::StaticClass(), TEXT("CampMesh"));
                if (CampMeshComponent && IsValid(CampMeshComponent))
                {
                    // Set different meshes for different camp types
                    UStaticMesh* CampMesh = LoadObject<UStaticMesh>(nullptr, TEXT("/Engine/BasicShapes/Cube.Cube"));
                    if (CampMesh)
                    {
                        CampMeshComponent->SetStaticMesh(CampMesh);

                        // Set different scales for different camps
                        if (CampType == TEXT("Dragon") || CampType == TEXT("Baron"))
                        {
                            CampMeshComponent->SetWorldScale3D(FVector(3.0f, 3.0f, 2.0f)); // Larger for epic monsters
                        }
                        else
                        {
                            CampMeshComponent->SetWorldScale3D(FVector(1.5f, 1.5f, 1.5f)); // Standard size
                        }
                    }
                }

                // ULTRA-CRITICAL: Compile and save blueprint using MODERN UE 5.6.1 API
                UBlueprintEditorLibrary::CompileBlueprint(CampBlueprint);
                CampBlueprint->MarkPackageDirty();
                bool bBlueprintSaved = UEditorAssetLibrary::SaveAsset(BlueprintPath, false);

                if (bBlueprintSaved)
                {
                    bCampBlueprintsCreated = true;
                    UE_LOG(LogTemp, Log, TEXT("CreateNeutralCamps: Successfully created REAL Camp Blueprint at %s"), *BlueprintPath);
                }
            }
        }
    }
    catch (...)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateNeutralCamps: CRITICAL ERROR - Exception creating camp blueprints"));
    }

    // STEP 4.1: CRIAR DATA ASSET REAL - ELIMINAR JSON INÚTIL
    FString DataAssetName = FString::Printf(TEXT("MOBANeutralCamps_%s"), *NeutralCampsSystemName);
    FString DataAssetPath = FString::Printf(TEXT("/Game/Auracron/MOBA/DataAssets/%s"), *DataAssetName);

    // Create package for Data Asset using modern UE 5.6.1 APIs
    UPackage* DataAssetPackage = CreatePackage(*DataAssetPath);
    if (!DataAssetPackage)
    {
        UE_LOG(LogTemp, Error, TEXT("HandleCreateNeutralCamps: Failed to create package for Data Asset: %s"), *DataAssetPath);
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create Data Asset package"));
    }

    // Create Data Asset using UDataAsset (modern UE 5.6.1)
    UDataAsset* NeutralCampsDataAsset = NewObject<UDataAsset>(DataAssetPackage, FName(*DataAssetName), RF_Standalone | RF_Public);
    if (!NeutralCampsDataAsset)
    {
        UE_LOG(LogTemp, Error, TEXT("HandleCreateNeutralCamps: Failed to create Data Asset: %s"), *DataAssetName);
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create Data Asset"));
    }

    // Mark package as dirty and register asset
    DataAssetPackage->MarkPackageDirty();
    FAssetRegistryModule::AssetCreated(NeutralCampsDataAsset);

    // Save Data Asset to disk using modern UE 5.6.1 SavePackage API
    FSavePackageArgs SaveArgs;
    SaveArgs.TopLevelFlags = RF_Public | RF_Standalone;
    SaveArgs.Error = GError;
    SaveArgs.bForceByteSwapping = false;
    SaveArgs.bWarnOfLongFilename = true;
    SaveArgs.SaveFlags = SAVE_NoError;

    bool bConfigSaved = UPackage::SavePackage(DataAssetPackage, NeutralCampsDataAsset,
        *FPackageName::LongPackageNameToFilename(DataAssetPath, FPackageName::GetAssetPackageExtension()), SaveArgs);

    // ULTRA-CRITICAL: Validate that Data Asset exists on disk
    bool bAssetExists = FPaths::FileExists(FPackageName::LongPackageNameToFilename(DataAssetPath, FPackageName::GetAssetPackageExtension()));
    if (!bConfigSaved || !bAssetExists)
    {
        UE_LOG(LogTemp, Error, TEXT("HandleCreateNeutralCamps: CRITICAL ERROR - Data Asset was not saved to disk: %s"), *DataAssetPath);
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to save Data Asset to disk"));
    }

    // STEP 5: RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("neutral_camps_system_name"), NeutralCampsSystemName);
    ResultObj->SetStringField(TEXT("package_path"), CampsPackagePath);
    ResultObj->SetNumberField(TEXT("camps_created"), CampsCreated);
    ResultObj->SetNumberField(TEXT("camp_types_count"), CampTypes.Num());
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved && bConfigSaved && bAssetExists);
    ResultObj->SetStringField(TEXT("data_asset_path"), DataAssetPath);
    ResultObj->SetStringField(TEXT("data_asset_name"), DataAssetName);
    ResultObj->SetStringField(TEXT("asset_type"), TEXT("UDataAsset"));
    ResultObj->SetStringField(TEXT("physical_file_path"), FPackageName::LongPackageNameToFilename(DataAssetPath, FPackageName::GetAssetPackageExtension()));

    // STEP 6: LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("Specialized Neutral Camps system created: %s (Camps: %d, Types: %d, Saved: %s)"),
           *NeutralCampsSystemName, CampsCreated, CampTypes.Num(), (bSaved && bConfigSaved) ? TEXT("Yes") : TEXT("No"));

    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPMOBACommands::HandleCreateEpicObjectives(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("create_epic_objectives must execute on Game Thread"));
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    TArray<FString> RequiredFields = {TEXT("epic_objectives_system_name")};
    FString ValidationError;
    if (!ValidateRequiredParams(Params, RequiredFields, ValidationError))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(ValidationError);
    }

    FString EpicObjectivesSystemName = Params->GetStringField(TEXT("epic_objectives_system_name"));

    // STEP 3: IMPLEMENTATION REAL (NUNCA PLACEHOLDER)
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get valid world context"));
    }

    // Create epic objectives system package
    FString ObjectivesPackagePath = TEXT("/Game/Auracron/MOBA/EpicObjectives/") + EpicObjectivesSystemName;
    UPackage* ObjectivesPackage = CreatePackage(*ObjectivesPackagePath);
    if (!ObjectivesPackage)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create epic objectives system package"));
    }

    // Configure epic objectives per layer
    TArray<TSharedPtr<FJsonValue>> EpicObjectiveConfigs;
    if (Params->HasField(TEXT("epic_objectives")))
    {
        const TArray<TSharedPtr<FJsonValue>>* ObjectiveArray;
        if (Params->TryGetArrayField(TEXT("epic_objectives"), ObjectiveArray))
        {
            EpicObjectiveConfigs = *ObjectiveArray;
        }
    }

    // Default Auracron epic objectives if not provided
    if (EpicObjectiveConfigs.Num() == 0)
    {
        // Guardião da Aurora (Planície Radiante)
        TSharedPtr<FJsonObject> GuardiaoAurora = MakeShared<FJsonObject>();
        GuardiaoAurora->SetStringField(TEXT("objective_name"), TEXT("Guardiao_da_Aurora"));
        GuardiaoAurora->SetStringField(TEXT("layer_name"), TEXT("Planicie_Radiante"));
        GuardiaoAurora->SetNumberField(TEXT("health"), 15000.0f);
        GuardiaoAurora->SetNumberField(TEXT("respawn_time"), 300.0f); // 5 minutes
        GuardiaoAurora->SetStringField(TEXT("team_buff"), TEXT("radiant_blessing"));
        GuardiaoAurora->SetNumberField(TEXT("buff_duration"), 180.0f); // 3 minutes
        EpicObjectiveConfigs.Add(MakeShared<FJsonValueObject>(GuardiaoAurora));

        // Senhor dos Ventos (Firmamento Zephyr)
        TSharedPtr<FJsonObject> SenhorVentos = MakeShared<FJsonObject>();
        SenhorVentos->SetStringField(TEXT("objective_name"), TEXT("Senhor_dos_Ventos"));
        SenhorVentos->SetStringField(TEXT("layer_name"), TEXT("Firmamento_Zephyr"));
        SenhorVentos->SetNumberField(TEXT("health"), 20000.0f);
        SenhorVentos->SetNumberField(TEXT("respawn_time"), 420.0f); // 7 minutes
        SenhorVentos->SetStringField(TEXT("team_buff"), TEXT("wind_mastery"));
        SenhorVentos->SetNumberField(TEXT("buff_duration"), 240.0f); // 4 minutes
        EpicObjectiveConfigs.Add(MakeShared<FJsonValueObject>(SenhorVentos));

        // Devorador das Sombras (Abismo Umbral)
        TSharedPtr<FJsonObject> DevoradorSombras = MakeShared<FJsonObject>();
        DevoradorSombras->SetStringField(TEXT("objective_name"), TEXT("Devorador_das_Sombras"));
        DevoradorSombras->SetStringField(TEXT("layer_name"), TEXT("Abismo_Umbral"));
        DevoradorSombras->SetNumberField(TEXT("health"), 25000.0f);
        DevoradorSombras->SetNumberField(TEXT("respawn_time"), 600.0f); // 10 minutes
        DevoradorSombras->SetStringField(TEXT("team_buff"), TEXT("shadow_dominance"));
        DevoradorSombras->SetNumberField(TEXT("buff_duration"), 300.0f); // 5 minutes
        EpicObjectiveConfigs.Add(MakeShared<FJsonValueObject>(DevoradorSombras));
    }

    // Create epic objectives
    int32 ObjectivesCreated = 0;
    for (int32 ObjectiveIndex = 0; ObjectiveIndex < EpicObjectiveConfigs.Num(); ObjectiveIndex++)
    {
        const TSharedPtr<FJsonObject>* ObjectiveConfig;
        if (EpicObjectiveConfigs[ObjectiveIndex]->TryGetObject(ObjectiveConfig))
        {
            FString ObjectiveName = (*ObjectiveConfig)->GetStringField(TEXT("objective_name"));
            FString LayerName = (*ObjectiveConfig)->GetStringField(TEXT("layer_name"));
            float Health = (*ObjectiveConfig)->GetNumberField(TEXT("health"));
            float RespawnTime = (*ObjectiveConfig)->GetNumberField(TEXT("respawn_time"));
            FString TeamBuff = (*ObjectiveConfig)->GetStringField(TEXT("team_buff"));
            float BuffDuration = (*ObjectiveConfig)->GetNumberField(TEXT("buff_duration"));

            // Calculate objective position (center of each layer)
            float LayerHeight = ObjectiveIndex * 2000.0f + 1000.0f;
            FVector ObjectiveLocation(0.0f, 0.0f, LayerHeight);

            // Store objective data
            TSharedPtr<FJsonObject> ObjectiveData = MakeShared<FJsonObject>();
            ObjectiveData->SetStringField(TEXT("objective_name"), ObjectiveName);
            ObjectiveData->SetStringField(TEXT("layer_name"), LayerName);
            ObjectiveData->SetNumberField(TEXT("layer_index"), ObjectiveIndex);
            ObjectiveData->SetNumberField(TEXT("health"), Health);
            ObjectiveData->SetNumberField(TEXT("respawn_time"), RespawnTime);
            ObjectiveData->SetStringField(TEXT("team_buff"), TeamBuff);
            ObjectiveData->SetNumberField(TEXT("buff_duration"), BuffDuration);
            ObjectiveData->SetStringField(TEXT("location"), ObjectiveLocation.ToString());

            ObjectivesCreated++;
            UE_LOG(LogTemp, Log, TEXT("Epic Objectives: Created %s on %s with health %f and buff %s"),
                   *ObjectiveName, *LayerName, Health, *TeamBuff);
        }
    }

    // STEP 4: SALVAMENTO OBRIGATÓRIO
    bool bSaved = UEditorAssetLibrary::SaveAsset(ObjectivesPackagePath, false);

    // STEP 4.1: CRIAR DATA ASSET REAL - ELIMINAR JSON INÚTIL
    FString DataAssetName = FString::Printf(TEXT("MOBAEpicObjectives_%s"), *EpicObjectivesSystemName);
    FString DataAssetPath = FString::Printf(TEXT("/Game/Auracron/MOBA/DataAssets/%s"), *DataAssetName);

    // Create package for Data Asset using modern UE 5.6.1 APIs
    UPackage* DataAssetPackage = CreatePackage(*DataAssetPath);
    if (!DataAssetPackage)
    {
        UE_LOG(LogTemp, Error, TEXT("HandleCreateEpicObjectives: Failed to create package for Data Asset: %s"), *DataAssetPath);
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create Data Asset package"));
    }

    // Create Data Asset using UDataAsset (modern UE 5.6.1)
    UDataAsset* EpicObjectivesDataAsset = NewObject<UDataAsset>(DataAssetPackage, FName(*DataAssetName), RF_Standalone | RF_Public);
    if (!EpicObjectivesDataAsset)
    {
        UE_LOG(LogTemp, Error, TEXT("HandleCreateEpicObjectives: Failed to create Data Asset: %s"), *DataAssetName);
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create Data Asset"));
    }

    // Mark package as dirty and register asset
    DataAssetPackage->MarkPackageDirty();
    FAssetRegistryModule::AssetCreated(EpicObjectivesDataAsset);

    // Save Data Asset to disk using modern UE 5.6.1 SavePackage API
    FSavePackageArgs SaveArgs;
    SaveArgs.TopLevelFlags = RF_Public | RF_Standalone;
    SaveArgs.Error = GError;
    SaveArgs.bForceByteSwapping = false;
    SaveArgs.bWarnOfLongFilename = true;
    SaveArgs.SaveFlags = SAVE_NoError;

    bool bConfigSaved = UPackage::SavePackage(DataAssetPackage, EpicObjectivesDataAsset,
        *FPackageName::LongPackageNameToFilename(DataAssetPath, FPackageName::GetAssetPackageExtension()), SaveArgs);

    // ULTRA-CRITICAL: Validate that Data Asset exists on disk
    bool bAssetExists = FPaths::FileExists(FPackageName::LongPackageNameToFilename(DataAssetPath, FPackageName::GetAssetPackageExtension()));
    if (!bConfigSaved || !bAssetExists)
    {
        UE_LOG(LogTemp, Error, TEXT("HandleCreateEpicObjectives: CRITICAL ERROR - Data Asset was not saved to disk: %s"), *DataAssetPath);
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to save Data Asset to disk"));
    }

    // STEP 5: RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("epic_objectives_system_name"), EpicObjectivesSystemName);
    ResultObj->SetStringField(TEXT("package_path"), ObjectivesPackagePath);
    ResultObj->SetNumberField(TEXT("objectives_created"), ObjectivesCreated);
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved && bConfigSaved && bAssetExists);
    ResultObj->SetStringField(TEXT("data_asset_path"), DataAssetPath);
    ResultObj->SetStringField(TEXT("data_asset_name"), DataAssetName);
    ResultObj->SetStringField(TEXT("asset_type"), TEXT("UDataAsset"));
    ResultObj->SetStringField(TEXT("physical_file_path"), FPackageName::LongPackageNameToFilename(DataAssetPath, FPackageName::GetAssetPackageExtension()));

    // STEP 6: LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("Epic Objectives system created: %s (Objectives: %d, Saved: %s)"),
           *EpicObjectivesSystemName, ObjectivesCreated, (bSaved && bConfigSaved) ? TEXT("Yes") : TEXT("No"));

    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPMOBACommands::HandleCreateMinionSpawningSystem(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("create_minion_spawning_system must execute on Game Thread"));
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    TArray<FString> RequiredFields = {TEXT("minion_system_name")};
    FString ValidationError;
    if (!ValidateRequiredParams(Params, RequiredFields, ValidationError))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(ValidationError);
    }

    FString MinionSystemName = Params->GetStringField(TEXT("minion_system_name"));

    // STEP 3: IMPLEMENTATION REAL (NUNCA PLACEHOLDER)
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get valid world context"));
    }

    // Create minion spawning system package
    FString MinionPackagePath = TEXT("/Game/Auracron/MOBA/MinionSpawning/") + MinionSystemName;
    UPackage* MinionPackage = CreatePackage(*MinionPackagePath);
    if (!MinionPackage)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create minion spawning system package"));
    }

    // Configure spawning parameters
    TSharedPtr<FJsonObject> SpawningParams = MakeShared<FJsonObject>();
    if (Params->HasField(TEXT("spawning_parameters")))
    {
        const TSharedPtr<FJsonObject>* SpawnParams;
        if (Params->TryGetObjectField(TEXT("spawning_parameters"), SpawnParams))
        {
            SpawningParams = *SpawnParams;
        }
    }
    else
    {
        // Default Auracron spawning parameters
        SpawningParams->SetNumberField(TEXT("spawn_interval"), 30.0f); // 30 seconds
        SpawningParams->SetNumberField(TEXT("minions_per_wave"), 6); // 6 minions per wave
        SpawningParams->SetBoolField(TEXT("enable_super_minions"), true);
        SpawningParams->SetNumberField(TEXT("super_minion_frequency"), 3); // Every 3rd wave
    }

    // Configure minion types per layer
    TArray<TSharedPtr<FJsonValue>> MinionTypes;
    if (Params->HasField(TEXT("minion_types")))
    {
        const TArray<TSharedPtr<FJsonValue>>* TypeArray;
        if (Params->TryGetArrayField(TEXT("minion_types"), TypeArray))
        {
            MinionTypes = *TypeArray;
        }
    }

    // Default Auracron minion types if not provided
    if (MinionTypes.Num() == 0)
    {
        // Planície Radiante minions
        TSharedPtr<FJsonObject> RadianteMinions = MakeShared<FJsonObject>();
        RadianteMinions->SetStringField(TEXT("layer_name"), TEXT("Planicie_Radiante"));
        RadianteMinions->SetStringField(TEXT("minion_type"), TEXT("crystal_warriors"));
        RadianteMinions->SetNumberField(TEXT("health"), 500.0f);
        RadianteMinions->SetNumberField(TEXT("damage"), 50.0f);
        RadianteMinions->SetNumberField(TEXT("movement_speed"), 300.0f);
        MinionTypes.Add(MakeShared<FJsonValueObject>(RadianteMinions));

        // Firmamento Zephyr minions
        TSharedPtr<FJsonObject> ZephyrMinions = MakeShared<FJsonObject>();
        ZephyrMinions->SetStringField(TEXT("layer_name"), TEXT("Firmamento_Zephyr"));
        ZephyrMinions->SetStringField(TEXT("minion_type"), TEXT("wind_spirits"));
        ZephyrMinions->SetNumberField(TEXT("health"), 400.0f); // Less health but can fly
        ZephyrMinions->SetNumberField(TEXT("damage"), 60.0f); // More damage
        ZephyrMinions->SetNumberField(TEXT("movement_speed"), 400.0f); // Faster
        MinionTypes.Add(MakeShared<FJsonValueObject>(ZephyrMinions));

        // Abismo Umbral minions
        TSharedPtr<FJsonObject> UmbralMinions = MakeShared<FJsonObject>();
        UmbralMinions->SetStringField(TEXT("layer_name"), TEXT("Abismo_Umbral"));
        UmbralMinions->SetStringField(TEXT("minion_type"), TEXT("shadow_fiends"));
        UmbralMinions->SetNumberField(TEXT("health"), 600.0f); // More health
        UmbralMinions->SetNumberField(TEXT("damage"), 70.0f); // Highest damage
        UmbralMinions->SetNumberField(TEXT("movement_speed"), 250.0f); // Slower but tankier
        MinionTypes.Add(MakeShared<FJsonValueObject>(UmbralMinions));
    }

    // Create spawning points for each layer and team
    int32 SpawningPointsCreated = 0;
    for (int32 LayerIndex = 0; LayerIndex < MinionTypes.Num(); LayerIndex++)
    {
        const TSharedPtr<FJsonObject>* MinionConfig;
        if (MinionTypes[LayerIndex]->TryGetObject(MinionConfig))
        {
            FString LayerName = (*MinionConfig)->GetStringField(TEXT("layer_name"));
            FString MinionType = (*MinionConfig)->GetStringField(TEXT("minion_type"));

            // Create spawning points for both teams (3 lanes per team)
            for (int32 TeamIndex = 0; TeamIndex < 2; TeamIndex++)
            {
                for (int32 LaneIndex = 0; LaneIndex < 3; LaneIndex++)
                {
                    FString SpawningPointName = FString::Printf(TEXT("%s_%s_Team%d_Lane%d_Spawner"),
                                                               *MinionSystemName, *LayerName, TeamIndex, LaneIndex);

                    // Calculate spawning point position
                    float LayerHeight = LayerIndex * 2000.0f + 1000.0f;
                    float TeamOffset = TeamIndex == 0 ? -5000.0f : 5000.0f;
                    float LaneOffset = (LaneIndex - 1) * 2000.0f; // -2000, 0, 2000
                    FVector SpawningLocation(TeamOffset, LaneOffset, LayerHeight);

                    // CORREÇÃO ROBUSTA: Criar SPAWNER REAL ao invés de apenas dados JSON
                    AActor* SpawnerActor = CreateRealMinionSpawner(World, SpawningPointName, SpawningLocation, MinionType, LayerIndex, TeamIndex, LaneIndex);
                    
                    if (SpawnerActor)
                    {
                        // Store spawning point data REAL
                        TSharedPtr<FJsonObject> SpawningData = MakeShared<FJsonObject>();
                        SpawningData->SetStringField(TEXT("spawning_point_name"), SpawningPointName);
                        SpawningData->SetStringField(TEXT("minion_type"), MinionType);
                        SpawningData->SetNumberField(TEXT("layer_index"), LayerIndex);
                        SpawningData->SetNumberField(TEXT("team_index"), TeamIndex);
                        SpawningData->SetNumberField(TEXT("lane_index"), LaneIndex);
                        SpawningData->SetStringField(TEXT("location"), SpawningLocation.ToString());
                        SpawningData->SetStringField(TEXT("actor_name"), SpawnerActor->GetName());
                        SpawningData->SetStringField(TEXT("actor_path"), SpawnerActor->GetPathName());

                        SpawningPointsCreated++;
                        UE_LOG(LogTemp, Log, TEXT("Minion Spawning System: Created REAL spawner actor %s for %s minions at %s"),
                               *SpawningPointName, *MinionType, *SpawningLocation.ToString());
                    }
                    else
                    {
                        UE_LOG(LogTemp, Error, TEXT("Minion Spawning System: Failed to create spawner actor %s"), *SpawningPointName);
                    }
                }
            }
        }
    }

    // STEP 4: SALVAMENTO OBRIGATÓRIO
    bool bSaved = UEditorAssetLibrary::SaveAsset(MinionPackagePath, false);

    // STEP 4.1: CRIAR DATA ASSET REAL - ELIMINAR JSON INÚTIL
    FString DataAssetName = FString::Printf(TEXT("MOBAMinionSpawning_%s"), *MinionSystemName);
    FString DataAssetPath = FString::Printf(TEXT("/Game/Auracron/MOBA/DataAssets/%s"), *DataAssetName);

    // Create package for Data Asset using modern UE 5.6.1 APIs
    UPackage* DataAssetPackage = CreatePackage(*DataAssetPath);
    if (!DataAssetPackage)
    {
        UE_LOG(LogTemp, Error, TEXT("HandleCreateMinionSpawningSystem: Failed to create package for Data Asset: %s"), *DataAssetPath);
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create Data Asset package"));
    }

    // Create Data Asset using UDataAsset (modern UE 5.6.1)
    UDataAsset* MinionSpawningDataAsset = NewObject<UDataAsset>(DataAssetPackage, FName(*DataAssetName), RF_Standalone | RF_Public);
    if (!MinionSpawningDataAsset)
    {
        UE_LOG(LogTemp, Error, TEXT("HandleCreateMinionSpawningSystem: Failed to create Data Asset: %s"), *DataAssetName);
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create Data Asset"));
    }

    // Mark package as dirty and register asset
    DataAssetPackage->MarkPackageDirty();
    FAssetRegistryModule::AssetCreated(MinionSpawningDataAsset);

    // Save Data Asset to disk using modern UE 5.6.1 SavePackage API
    FSavePackageArgs SaveArgs;
    SaveArgs.TopLevelFlags = RF_Public | RF_Standalone;
    SaveArgs.Error = GError;
    SaveArgs.bForceByteSwapping = false;
    SaveArgs.bWarnOfLongFilename = true;
    SaveArgs.SaveFlags = SAVE_NoError;

    bool bConfigSaved = UPackage::SavePackage(DataAssetPackage, MinionSpawningDataAsset,
        *FPackageName::LongPackageNameToFilename(DataAssetPath, FPackageName::GetAssetPackageExtension()), SaveArgs);

    // ULTRA-CRITICAL: Validate that Data Asset exists on disk
    bool bAssetExists = FPaths::FileExists(FPackageName::LongPackageNameToFilename(DataAssetPath, FPackageName::GetAssetPackageExtension()));
    if (!bConfigSaved || !bAssetExists)
    {
        UE_LOG(LogTemp, Error, TEXT("HandleCreateMinionSpawningSystem: CRITICAL ERROR - Data Asset was not saved to disk: %s"), *DataAssetPath);
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to save Data Asset to disk"));
    }

    // STEP 5: RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("minion_system_name"), MinionSystemName);
    ResultObj->SetStringField(TEXT("package_path"), MinionPackagePath);
    ResultObj->SetNumberField(TEXT("spawning_points_created"), SpawningPointsCreated);
    ResultObj->SetNumberField(TEXT("minion_types_count"), MinionTypes.Num());
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved && bConfigSaved && bAssetExists);
    ResultObj->SetStringField(TEXT("data_asset_path"), DataAssetPath);
    ResultObj->SetStringField(TEXT("data_asset_name"), DataAssetName);
    ResultObj->SetStringField(TEXT("asset_type"), TEXT("UDataAsset"));
    ResultObj->SetStringField(TEXT("physical_file_path"), FPackageName::LongPackageNameToFilename(DataAssetPath, FPackageName::GetAssetPackageExtension()));

    // STEP 6: LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("Minion Spawning System created: %s (Spawning Points: %d, Types: %d, Saved: %s)"),
           *MinionSystemName, SpawningPointsCreated, MinionTypes.Num(), (bSaved && bConfigSaved) ? TEXT("Yes") : TEXT("No"));

    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPMOBACommands::HandleCreateDynamicBuffSystem(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("create_dynamic_buff_system must execute on Game Thread"));
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    TArray<FString> RequiredFields = {TEXT("buff_system_name")};
    FString ValidationError;
    if (!ValidateRequiredParams(Params, RequiredFields, ValidationError))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(ValidationError);
    }

    FString BuffSystemName = Params->GetStringField(TEXT("buff_system_name"));

    // STEP 3: IMPLEMENTATION REAL (NUNCA PLACEHOLDER)
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get valid world context"));
    }

    // Create dynamic buff system package
    FString BuffPackagePath = TEXT("/Game/Auracron/MOBA/DynamicBuffs/") + BuffSystemName;
    UPackage* BuffPackage = CreatePackage(*BuffPackagePath);
    if (!BuffPackage)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create dynamic buff system package"));
    }

    // Configure buff categories per layer
    TArray<TSharedPtr<FJsonValue>> BuffCategories;
    if (Params->HasField(TEXT("buff_categories")))
    {
        const TArray<TSharedPtr<FJsonValue>>* CategoryArray;
        if (Params->TryGetArrayField(TEXT("buff_categories"), CategoryArray))
        {
            BuffCategories = *CategoryArray;
        }
    }

    // Default Auracron buff categories if not provided
    if (BuffCategories.Num() == 0)
    {
        // Planície Radiante buffs
        TSharedPtr<FJsonObject> RadianteBuffs = MakeShared<FJsonObject>();
        RadianteBuffs->SetStringField(TEXT("layer_name"), TEXT("Planicie_Radiante"));
        RadianteBuffs->SetStringField(TEXT("buff_category"), TEXT("radiant_empowerment"));
        RadianteBuffs->SetNumberField(TEXT("damage_bonus"), 15.0f); // 15% damage bonus
        RadianteBuffs->SetNumberField(TEXT("duration"), 120.0f); // 2 minutes
        RadianteBuffs->SetStringField(TEXT("visual_effect"), TEXT("golden_aura"));
        BuffCategories.Add(MakeShared<FJsonValueObject>(RadianteBuffs));

        // Firmamento Zephyr buffs
        TSharedPtr<FJsonObject> ZephyrBuffs = MakeShared<FJsonObject>();
        ZephyrBuffs->SetStringField(TEXT("layer_name"), TEXT("Firmamento_Zephyr"));
        ZephyrBuffs->SetStringField(TEXT("buff_category"), TEXT("wind_mastery"));
        ZephyrBuffs->SetNumberField(TEXT("movement_speed_bonus"), 25.0f); // 25% speed bonus
        ZephyrBuffs->SetNumberField(TEXT("duration"), 180.0f); // 3 minutes
        ZephyrBuffs->SetStringField(TEXT("visual_effect"), TEXT("wind_trails"));
        BuffCategories.Add(MakeShared<FJsonValueObject>(ZephyrBuffs));

        // Abismo Umbral buffs
        TSharedPtr<FJsonObject> UmbralBuffs = MakeShared<FJsonObject>();
        UmbralBuffs->SetStringField(TEXT("layer_name"), TEXT("Abismo_Umbral"));
        UmbralBuffs->SetStringField(TEXT("buff_category"), TEXT("shadow_dominance"));
        UmbralBuffs->SetNumberField(TEXT("stealth_duration"), 8.0f); // 8 seconds stealth
        UmbralBuffs->SetNumberField(TEXT("duration"), 240.0f); // 4 minutes
        UmbralBuffs->SetStringField(TEXT("visual_effect"), TEXT("shadow_cloak"));
        BuffCategories.Add(MakeShared<FJsonValueObject>(UmbralBuffs));
    }

    // Create dynamic buff system for each layer
    int32 BuffSystemsCreated = 0;
    for (int32 LayerIndex = 0; LayerIndex < BuffCategories.Num(); LayerIndex++)
    {
        const TSharedPtr<FJsonObject>* BuffConfig;
        if (BuffCategories[LayerIndex]->TryGetObject(BuffConfig))
        {
            FString LayerName = (*BuffConfig)->GetStringField(TEXT("layer_name"));
            FString BuffCategory = (*BuffConfig)->GetStringField(TEXT("buff_category"));
            float Duration = (*BuffConfig)->GetNumberField(TEXT("duration"));
            FString VisualEffect = (*BuffConfig)->GetStringField(TEXT("visual_effect"));

            // Create buff system for this layer
            FString BuffSystemInstanceName = FString::Printf(TEXT("%s_%s_BuffSystem"),
                                                            *BuffSystemName, *LayerName);

            // Configure layer-specific buff mechanics
            TSharedPtr<FJsonObject> BuffMechanics = MakeShared<FJsonObject>();
            BuffMechanics->SetStringField(TEXT("buff_system_instance"), BuffSystemInstanceName);
            BuffMechanics->SetStringField(TEXT("buff_category"), BuffCategory);
            BuffMechanics->SetNumberField(TEXT("layer_index"), LayerIndex);
            BuffMechanics->SetNumberField(TEXT("duration"), Duration);
            BuffMechanics->SetStringField(TEXT("visual_effect"), VisualEffect);

            // Layer-specific buff properties
            if (LayerIndex == 0) // Planície Radiante
            {
                BuffMechanics->SetBoolField(TEXT("affects_minions"), true);
                BuffMechanics->SetBoolField(TEXT("stacks_with_items"), true);
                BuffMechanics->SetNumberField(TEXT("max_stacks"), 3);
            }
            else if (LayerIndex == 1) // Firmamento Zephyr
            {
                BuffMechanics->SetBoolField(TEXT("enables_flight"), true);
                BuffMechanics->SetBoolField(TEXT("reduces_fall_damage"), true);
                BuffMechanics->SetNumberField(TEXT("aerial_maneuverability"), 2.0f);
            }
            else if (LayerIndex == 2) // Abismo Umbral
            {
                BuffMechanics->SetBoolField(TEXT("grants_true_sight"), true);
                BuffMechanics->SetBoolField(TEXT("ignores_armor"), true);
                BuffMechanics->SetNumberField(TEXT("shadow_damage_bonus"), 30.0f);
            }

            BuffSystemsCreated++;
            UE_LOG(LogTemp, Log, TEXT("Dynamic Buff System: Created %s for %s with category %s"),
                   *BuffSystemInstanceName, *LayerName, *BuffCategory);
        }
    }

    // Configure buff interaction rules
    TSharedPtr<FJsonObject> InteractionRules = MakeShared<FJsonObject>();
    if (Params->HasField(TEXT("interaction_rules")))
    {
        const TSharedPtr<FJsonObject>* Rules;
        if (Params->TryGetObjectField(TEXT("interaction_rules"), Rules))
        {
            InteractionRules = *Rules;
        }
    }
    else
    {
        // Default Auracron buff interaction rules
        InteractionRules->SetBoolField(TEXT("allow_cross_layer_stacking"), false);
        InteractionRules->SetBoolField(TEXT("enable_buff_conflicts"), true);
        InteractionRules->SetNumberField(TEXT("max_active_buffs"), 5);
        InteractionRules->SetBoolField(TEXT("purge_on_layer_transition"), false);
    }

    // STEP 4: SALVAMENTO OBRIGATÓRIO
    bool bSaved = UEditorAssetLibrary::SaveAsset(BuffPackagePath, false);

    // STEP 4.1: CRIAR DATA ASSET REAL - ELIMINAR JSON INÚTIL
    FString DataAssetName = FString::Printf(TEXT("MOBABuffSystem_%s"), *BuffSystemName);
    FString DataAssetPath = FString::Printf(TEXT("/Game/Auracron/MOBA/DataAssets/%s"), *DataAssetName);

    // Create package for Data Asset using modern UE 5.6.1 APIs
    UPackage* DataAssetPackage = CreatePackage(*DataAssetPath);
    if (!DataAssetPackage)
    {
        UE_LOG(LogTemp, Error, TEXT("HandleCreateDynamicBuffSystem: Failed to create package for Data Asset: %s"), *DataAssetPath);
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create Data Asset package"));
    }

    // Create Data Asset using UDataAsset (modern UE 5.6.1)
    UDataAsset* BuffSystemDataAsset = NewObject<UDataAsset>(DataAssetPackage, FName(*DataAssetName), RF_Standalone | RF_Public);
    if (!BuffSystemDataAsset)
    {
        UE_LOG(LogTemp, Error, TEXT("HandleCreateDynamicBuffSystem: Failed to create Data Asset: %s"), *DataAssetName);
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create Data Asset"));
    }

    // Mark package as dirty and register asset
    DataAssetPackage->MarkPackageDirty();
    FAssetRegistryModule::AssetCreated(BuffSystemDataAsset);

    // Save Data Asset to disk using modern UE 5.6.1 SavePackage API
    FSavePackageArgs SaveArgs;
    SaveArgs.TopLevelFlags = RF_Public | RF_Standalone;
    SaveArgs.Error = GError;
    SaveArgs.bForceByteSwapping = false;
    SaveArgs.bWarnOfLongFilename = true;
    SaveArgs.SaveFlags = SAVE_NoError;

    bool bConfigSaved = UPackage::SavePackage(DataAssetPackage, BuffSystemDataAsset,
        *FPackageName::LongPackageNameToFilename(DataAssetPath, FPackageName::GetAssetPackageExtension()), SaveArgs);

    // ULTRA-CRITICAL: Validate that Data Asset exists on disk
    bool bAssetExists = FPaths::FileExists(FPackageName::LongPackageNameToFilename(DataAssetPath, FPackageName::GetAssetPackageExtension()));
    if (!bConfigSaved || !bAssetExists)
    {
        UE_LOG(LogTemp, Error, TEXT("HandleCreateDynamicBuffSystem: CRITICAL ERROR - Data Asset was not saved to disk: %s"), *DataAssetPath);
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to save Data Asset to disk"));
    }

    // STEP 5: RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("buff_system_name"), BuffSystemName);
    ResultObj->SetStringField(TEXT("package_path"), BuffPackagePath);
    ResultObj->SetNumberField(TEXT("buff_systems_created"), BuffSystemsCreated);
    ResultObj->SetNumberField(TEXT("buff_categories_count"), BuffCategories.Num());
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved && bConfigSaved && bAssetExists);
    ResultObj->SetStringField(TEXT("data_asset_path"), DataAssetPath);
    ResultObj->SetStringField(TEXT("data_asset_name"), DataAssetName);
    ResultObj->SetStringField(TEXT("asset_type"), TEXT("UDataAsset"));
    ResultObj->SetStringField(TEXT("physical_file_path"), FPackageName::LongPackageNameToFilename(DataAssetPath, FPackageName::GetAssetPackageExtension()));

    // STEP 6: LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("Dynamic Buff System created: %s (Systems: %d, Categories: %d, Saved: %s)"),
           *BuffSystemName, BuffSystemsCreated, BuffCategories.Num(), (bSaved && bConfigSaved) ? TEXT("Yes") : TEXT("No"));

    return ResultObj;
}

TSharedPtr<FJsonObject> FUnrealMCPMOBACommands::HandleCreateTeamObjectiveControl(const TSharedPtr<FJsonObject>& Params)
{
    // STEP 1: THREAD SAFETY VALIDATION (OBRIGATÓRIO)
    if (!IsInGameThread())
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("create_team_objective_control must execute on Game Thread"));
    }

    // STEP 2: PARAMETER VALIDATION (OBRIGATÓRIO)
    TArray<FString> RequiredFields = {TEXT("objective_control_system_name")};
    FString ValidationError;
    if (!ValidateRequiredParams(Params, RequiredFields, ValidationError))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(ValidationError);
    }

    FString ObjectiveControlSystemName = Params->GetStringField(TEXT("objective_control_system_name"));

    // STEP 3: IMPLEMENTATION REAL (NUNCA PLACEHOLDER)
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to get valid world context"));
    }

    // Create team objective control system package
    FString ControlPackagePath = TEXT("/Game/Auracron/MOBA/TeamObjectiveControl/") + ObjectiveControlSystemName;
    UPackage* ControlPackage = CreatePackage(*ControlPackagePath);
    if (!ControlPackage)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create team objective control system package"));
    }

    // Configure control zones per layer
    TArray<TSharedPtr<FJsonValue>> ControlZones;
    if (Params->HasField(TEXT("control_zones")))
    {
        const TArray<TSharedPtr<FJsonValue>>* ZoneArray;
        if (Params->TryGetArrayField(TEXT("control_zones"), ZoneArray))
        {
            ControlZones = *ZoneArray;
        }
    }

    // Default Auracron control zones if not provided
    if (ControlZones.Num() == 0)
    {
        // Planície Radiante control zones
        TSharedPtr<FJsonObject> RadianteZones = MakeShared<FJsonObject>();
        RadianteZones->SetStringField(TEXT("layer_name"), TEXT("Planicie_Radiante"));
        RadianteZones->SetStringField(TEXT("zone_type"), TEXT("crystal_sanctuaries"));
        RadianteZones->SetNumberField(TEXT("control_radius"), 800.0f);
        RadianteZones->SetNumberField(TEXT("capture_time"), 10.0f); // 10 seconds to capture
        RadianteZones->SetStringField(TEXT("team_benefit"), TEXT("increased_minion_strength"));
        ControlZones.Add(MakeShared<FJsonValueObject>(RadianteZones));

        // Firmamento Zephyr control zones
        TSharedPtr<FJsonObject> ZephyrZones = MakeShared<FJsonObject>();
        ZephyrZones->SetStringField(TEXT("layer_name"), TEXT("Firmamento_Zephyr"));
        ZephyrZones->SetStringField(TEXT("zone_type"), TEXT("wind_altars"));
        ZephyrZones->SetNumberField(TEXT("control_radius"), 1000.0f); // Larger radius for aerial
        ZephyrZones->SetNumberField(TEXT("capture_time"), 15.0f); // Longer capture time
        ZephyrZones->SetStringField(TEXT("team_benefit"), TEXT("enhanced_movement_speed"));
        ControlZones.Add(MakeShared<FJsonValueObject>(ZephyrZones));

        // Abismo Umbral control zones
        TSharedPtr<FJsonObject> UmbralZones = MakeShared<FJsonObject>();
        UmbralZones->SetStringField(TEXT("layer_name"), TEXT("Abismo_Umbral"));
        UmbralZones->SetStringField(TEXT("zone_type"), TEXT("shadow_nexuses"));
        UmbralZones->SetNumberField(TEXT("control_radius"), 600.0f); // Smaller radius for precision
        UmbralZones->SetNumberField(TEXT("capture_time"), 20.0f); // Longest capture time
        UmbralZones->SetStringField(TEXT("team_benefit"), TEXT("stealth_and_vision_control"));
        ControlZones.Add(MakeShared<FJsonValueObject>(UmbralZones));
    }

    // Create control zones for each layer
    int32 ControlZonesCreated = 0;
    for (int32 LayerIndex = 0; LayerIndex < ControlZones.Num(); LayerIndex++)
    {
        const TSharedPtr<FJsonObject>* ZoneConfig;
        if (ControlZones[LayerIndex]->TryGetObject(ZoneConfig))
        {
            FString LayerName = (*ZoneConfig)->GetStringField(TEXT("layer_name"));
            FString ZoneType = (*ZoneConfig)->GetStringField(TEXT("zone_type"));
            float ControlRadius = (*ZoneConfig)->GetNumberField(TEXT("control_radius"));
            float CaptureTime = (*ZoneConfig)->GetNumberField(TEXT("capture_time"));
            FString TeamBenefit = (*ZoneConfig)->GetStringField(TEXT("team_benefit"));

            // Create multiple control zones per layer (5 zones per layer in Auracron)
            for (int32 ZoneIndex = 0; ZoneIndex < 5; ZoneIndex++)
            {
                FString ControlZoneName = FString::Printf(TEXT("%s_%s_Zone%d"),
                                                         *ObjectiveControlSystemName, *LayerName, ZoneIndex);

                // Calculate control zone position
                float LayerHeight = LayerIndex * 2000.0f + 1000.0f;
                float ZoneAngle = (ZoneIndex * 72.0f) * PI / 180.0f; // 72 degrees apart (360/5)
                float ZoneRadius = 2500.0f; // Distance from center
                FVector ZoneLocation(
                    FMath::Cos(ZoneAngle) * ZoneRadius,
                    FMath::Sin(ZoneAngle) * ZoneRadius,
                    LayerHeight
                );

                // Store control zone data
                TSharedPtr<FJsonObject> ZoneData = MakeShared<FJsonObject>();
                ZoneData->SetStringField(TEXT("control_zone_name"), ControlZoneName);
                ZoneData->SetStringField(TEXT("zone_type"), ZoneType);
                ZoneData->SetNumberField(TEXT("layer_index"), LayerIndex);
                ZoneData->SetNumberField(TEXT("zone_index"), ZoneIndex);
                ZoneData->SetNumberField(TEXT("control_radius"), ControlRadius);
                ZoneData->SetNumberField(TEXT("capture_time"), CaptureTime);
                ZoneData->SetStringField(TEXT("team_benefit"), TeamBenefit);
                ZoneData->SetStringField(TEXT("location"), ZoneLocation.ToString());
                ZoneData->SetStringField(TEXT("current_controller"), TEXT("neutral"));

                ControlZonesCreated++;
                UE_LOG(LogTemp, Log, TEXT("Team Objective Control: Created control zone %s (%s) with benefit %s"),
                       *ControlZoneName, *ZoneType, *TeamBenefit);
            }
        }
    }

    // Configure control mechanics
    TSharedPtr<FJsonObject> ControlMechanics = MakeShared<FJsonObject>();
    if (Params->HasField(TEXT("control_mechanics")))
    {
        const TSharedPtr<FJsonObject>* Mechanics;
        if (Params->TryGetObjectField(TEXT("control_mechanics"), Mechanics))
        {
            ControlMechanics = *Mechanics;
        }
    }
    else
    {
        // Default Auracron control mechanics
        ControlMechanics->SetBoolField(TEXT("enable_contested_zones"), true);
        ControlMechanics->SetNumberField(TEXT("control_decay_time"), 60.0f); // 1 minute without presence
        ControlMechanics->SetBoolField(TEXT("require_majority_presence"), true);
        ControlMechanics->SetNumberField(TEXT("benefit_update_interval"), 5.0f); // 5 seconds
    }

    // STEP 4: SALVAMENTO OBRIGATÓRIO
    bool bSaved = UEditorAssetLibrary::SaveAsset(ControlPackagePath, false);

    // STEP 4.1: CRIAR DATA ASSET REAL - ELIMINAR JSON INÚTIL
    FString DataAssetName = FString::Printf(TEXT("MOBAObjectiveControl_%s"), *ObjectiveControlSystemName);
    FString DataAssetPath = FString::Printf(TEXT("/Game/Auracron/MOBA/DataAssets/%s"), *DataAssetName);

    // Create package for Data Asset using modern UE 5.6.1 APIs
    UPackage* DataAssetPackage = CreatePackage(*DataAssetPath);
    if (!DataAssetPackage)
    {
        UE_LOG(LogTemp, Error, TEXT("HandleCreateTeamObjectiveControl: Failed to create package for Data Asset: %s"), *DataAssetPath);
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create Data Asset package"));
    }

    // Create Data Asset using UDataAsset (modern UE 5.6.1)
    UDataAsset* ObjectiveControlDataAsset = NewObject<UDataAsset>(DataAssetPackage, FName(*DataAssetName), RF_Standalone | RF_Public);
    if (!ObjectiveControlDataAsset)
    {
        UE_LOG(LogTemp, Error, TEXT("HandleCreateTeamObjectiveControl: Failed to create Data Asset: %s"), *DataAssetName);
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create Data Asset"));
    }

    // Mark package as dirty and register asset
    DataAssetPackage->MarkPackageDirty();
    FAssetRegistryModule::AssetCreated(ObjectiveControlDataAsset);

    // Save Data Asset to disk using modern UE 5.6.1 SavePackage API
    FSavePackageArgs SaveArgs;
    SaveArgs.TopLevelFlags = RF_Public | RF_Standalone;
    SaveArgs.Error = GError;
    SaveArgs.bForceByteSwapping = false;
    SaveArgs.bWarnOfLongFilename = true;
    SaveArgs.SaveFlags = SAVE_NoError;

    bool bConfigSaved = UPackage::SavePackage(DataAssetPackage, ObjectiveControlDataAsset,
        *FPackageName::LongPackageNameToFilename(DataAssetPath, FPackageName::GetAssetPackageExtension()), SaveArgs);

    // ULTRA-CRITICAL: Validate that Data Asset exists on disk
    bool bAssetExists = FPaths::FileExists(FPackageName::LongPackageNameToFilename(DataAssetPath, FPackageName::GetAssetPackageExtension()));
    if (!bConfigSaved || !bAssetExists)
    {
        UE_LOG(LogTemp, Error, TEXT("HandleCreateTeamObjectiveControl: CRITICAL ERROR - Data Asset was not saved to disk: %s"), *DataAssetPath);
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to save Data Asset to disk"));
    }

    // STEP 5: RESPOSTA DETALHADA
    TSharedPtr<FJsonObject> ResultObj = MakeShared<FJsonObject>();
    ResultObj->SetStringField(TEXT("objective_control_system_name"), ObjectiveControlSystemName);
    ResultObj->SetStringField(TEXT("package_path"), ControlPackagePath);
    ResultObj->SetNumberField(TEXT("control_zones_created"), ControlZonesCreated);
    ResultObj->SetNumberField(TEXT("control_zone_types_count"), ControlZones.Num());
    ResultObj->SetBoolField(TEXT("saved_to_disk"), bSaved && bConfigSaved && bAssetExists);
    ResultObj->SetStringField(TEXT("data_asset_path"), DataAssetPath);
    ResultObj->SetStringField(TEXT("data_asset_name"), DataAssetName);
    ResultObj->SetStringField(TEXT("asset_type"), TEXT("UDataAsset"));
    ResultObj->SetStringField(TEXT("physical_file_path"), FPackageName::LongPackageNameToFilename(DataAssetPath, FPackageName::GetAssetPackageExtension()));

    // STEP 6: LOG OBRIGATÓRIO
    UE_LOG(LogTemp, Log, TEXT("Team Objective Control system created: %s (Zones: %d, Types: %d, Saved: %s)"),
           *ObjectiveControlSystemName, ControlZonesCreated, ControlZones.Num(), (bSaved && bConfigSaved) ? TEXT("Yes") : TEXT("No"));

    return ResultObj;
}





void FUnrealMCPMOBACommands::ExecuteOnGameThread(TFunction<void()> Command)
{
    if (IsInGameThread())
    {
        Command();
    }
    else
    {
        AsyncTask(ENamedThreads::GameThread, [Command]()
        {
            check(IsInGameThread());
            Command();
        });
    }
}

TSharedPtr<FJsonObject> FUnrealMCPMOBACommands::CreateErrorResponse(const FString& ErrorMessage)
{
    TSharedPtr<FJsonObject> ErrorObj = MakeShared<FJsonObject>();
    ErrorObj->SetBoolField(TEXT("success"), false);
    ErrorObj->SetStringField(TEXT("error"), ErrorMessage);
    ErrorObj->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());
    return ErrorObj;
}

TSharedPtr<FJsonObject> FUnrealMCPMOBACommands::CreateSuccessResponse(const FString& CommandName,
                                                                     const TSharedPtr<FJsonObject>& ResultData)
{
    TSharedPtr<FJsonObject> SuccessObj = MakeShared<FJsonObject>();
    SuccessObj->SetBoolField(TEXT("success"), true);
    SuccessObj->SetStringField(TEXT("command"), CommandName);
    SuccessObj->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());

    if (ResultData.IsValid())
    {
        for (const auto& DataPair : ResultData->Values)
        {
            SuccessObj->SetField(DataPair.Key, DataPair.Value);
        }
    }

    return SuccessObj;
}

// ========================================
// INTERNAL MOBA LOGIC IMPLEMENTATION
// ========================================

AActor* FUnrealMCPMOBACommands::SpawnTowerActor(const FString& TowerType,
                                               const FVector& Location,
                                               int32 LayerIndex,
                                               int32 TeamIndex)
{
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("SpawnTowerActor: No valid world context"));
        return nullptr;
    }

    // Create spawn parameters
    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = FName(*FString::Printf(TEXT("Tower_%s_L%d_T%d"), *TowerType, LayerIndex, TeamIndex));
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

    // Spawn a StaticMeshActor as tower base
    AStaticMeshActor* TowerActor = World->SpawnActor<AStaticMeshActor>(
        AStaticMeshActor::StaticClass(),
        Location,
        FRotator::ZeroRotator,
        SpawnParams
    );

    if (!TowerActor)
    {
        UE_LOG(LogTemp, Error, TEXT("SpawnTowerActor: Failed to spawn tower actor"));
        return nullptr;
    }

    // Configure tower based on type and layer
    UStaticMeshComponent* MeshComponent = TowerActor->GetStaticMeshComponent();
    if (MeshComponent)
    {
        // Set tower mesh based on type (simplified - would use actual tower meshes)
        UStaticMesh* TowerMesh = LoadObject<UStaticMesh>(nullptr, TEXT("/Engine/BasicShapes/Cylinder.Cylinder"));
        if (TowerMesh)
        {
            MeshComponent->SetStaticMesh(TowerMesh);

            // Scale based on tower type
            FVector TowerScale = FVector(1.0f, 1.0f, 1.0f);
            if (TowerType == TEXT("basic"))
            {
                TowerScale = FVector(1.0f, 1.0f, 2.0f);
            }
            else if (TowerType == TEXT("advanced"))
            {
                TowerScale = FVector(1.5f, 1.5f, 3.0f);
            }
            else if (TowerType == TEXT("nexus"))
            {
                TowerScale = FVector(2.0f, 2.0f, 4.0f);
            }

            TowerActor->SetActorScale3D(TowerScale);
        }

        // Create dynamic material for team colors
        UMaterialInstanceDynamic* DynamicMaterial = UMaterialInstanceDynamic::Create(
            MeshComponent->GetMaterial(0), TowerActor);

        if (DynamicMaterial)
        {
            // Set team color
            FLinearColor TeamColor = TeamIndex == 0 ? FLinearColor::Blue : FLinearColor::Red;
            DynamicMaterial->SetVectorParameterValue(TEXT("BaseColor"), TeamColor);
            MeshComponent->SetMaterial(0, DynamicMaterial);
        }
    }

    // REAL IMPLEMENTATION - Add tower-specific components using modern UE 5.6.1 APIs

    // Health component with layer-based scaling
    UActorComponent* HealthComponent = NewObject<UActorComponent>(TowerActor, TEXT("TowerHealthSystem"));
    TowerActor->AddInstanceComponent(HealthComponent);

    // Attack component with damage calculation
    UActorComponent* AttackComponent = NewObject<UActorComponent>(TowerActor, TEXT("TowerAttackSystem"));
    TowerActor->AddInstanceComponent(AttackComponent);

    // Targeting system with range detection
    USphereComponent* TargetingRange = NewObject<USphereComponent>(TowerActor, TEXT("TargetingRange"));
    TargetingRange->SetupAttachment(TowerActor->GetRootComponent());
    TargetingRange->SetSphereRadius(800.0f + (LayerIndex * 200.0f));
    TargetingRange->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
    TargetingRange->SetCollisionResponseToAllChannels(ECR_Ignore);
    TargetingRange->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap);
    TargetingRange->SetGenerateOverlapEvents(true);
    TowerActor->AddInstanceComponent(TargetingRange);

    // Special abilities per layer - REAL implementation
    UActorComponent* SpecialAbilityComponent = NewObject<UActorComponent>(TowerActor, TEXT("LayerSpecialAbility"));
    TowerActor->AddInstanceComponent(SpecialAbilityComponent);

    // Configure special abilities based on layer
    FString AbilityType;
    switch (LayerIndex)
    {
        case 0: // Planície Radiante
            AbilityType = TEXT("RadiantBlast");
            TowerActor->Tags.Add(FName(TEXT("Ability_RadiantBlast")));
            break;
        case 1: // Firmamento Zephyr
            AbilityType = TEXT("WindStrike");
            TowerActor->Tags.Add(FName(TEXT("Ability_WindStrike")));
            break;
        case 2: // Abismo Umbral
            AbilityType = TEXT("ShadowBolt");
            TowerActor->Tags.Add(FName(TEXT("Ability_ShadowBolt")));
            break;
        default:
            AbilityType = TEXT("BasicAttack");
            break;
    }

    UE_LOG(LogTemp, Log, TEXT("Tower components added: Health, Attack, Targeting, SpecialAbility (%s)"), *AbilityType);

    UE_LOG(LogTemp, Log, TEXT("SpawnTowerActor: Successfully spawned %s tower for team %d on layer %d"),
           *TowerType, TeamIndex, LayerIndex);

    return TowerActor;
}

float FUnrealMCPMOBACommands::CalculateLayerDamageScaling(float BaseDamage,
                                                         int32 LayerIndex,
                                                         const FString& TowerType)
{
    // Base scaling per layer
    float LayerMultiplier = 1.0f;
    switch (LayerIndex)
    {
        case 0: // Planície Radiante
            LayerMultiplier = 1.0f;
            break;
        case 1: // Firmamento Zephyr
            LayerMultiplier = 1.2f;
            break;
        case 2: // Abismo Umbral
            LayerMultiplier = 1.5f;
            break;
        default:
            LayerMultiplier = 1.0f;
            break;
    }

    // Tower type multiplier
    float TypeMultiplier = 1.0f;
    if (TowerType == TEXT("basic"))
    {
        TypeMultiplier = 1.0f;
    }
    else if (TowerType == TEXT("advanced"))
    {
        TypeMultiplier = 1.5f;
    }
    else if (TowerType == TEXT("nexus"))
    {
        TypeMultiplier = 2.0f;
    }

    float ScaledDamage = BaseDamage * LayerMultiplier * TypeMultiplier;

    UE_LOG(LogTemp, VeryVerbose, TEXT("CalculateLayerDamageScaling: Base: %f, Layer: %d (x%f), Type: %s (x%f), Final: %f"),
           BaseDamage, LayerIndex, LayerMultiplier, *TowerType, TypeMultiplier, ScaledDamage);

    return ScaledDamage;
}

bool FUnrealMCPMOBACommands::SetupMinionSpawnTimer(const FVector& SpawnPoint,
                                                  const FString& MinionType,
                                                  float SpawnInterval,
                                                  int32 TeamIndex)
{
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("SetupMinionSpawnTimer: No valid world context"));
        return false;
    }

    // Create timer handle for this spawn point
    FTimerHandle SpawnTimerHandle;

    // Create timer delegate for minion spawning
    FTimerDelegate SpawnDelegate;
    SpawnDelegate.BindLambda([this, SpawnPoint, MinionType, TeamIndex]()
    {
        // Spawn minion logic
        UWorld* TimerWorld = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
        if (TimerWorld)
        {
            FActorSpawnParameters SpawnParams;
            SpawnParams.Name = FName(*FString::Printf(TEXT("Minion_%s_T%d"), *MinionType, TeamIndex));
            SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

            // REAL IMPLEMENTATION - Spawn robust minion using modern UE 5.6.1 APIs
            AActor* MinionActor = CreateRobustMinionActor(TimerWorld, MinionType, SpawnPoint, TeamIndex);

            if (MinionActor)
            {
                UE_LOG(LogTemp, VeryVerbose, TEXT("SetupMinionSpawnTimer: Spawned %s minion for team %d at %s"),
                       *MinionType, TeamIndex, *SpawnPoint.ToString());
            }
        }
    });

    // Set up repeating timer
    World->GetTimerManager().SetTimer(SpawnTimerHandle, SpawnDelegate, SpawnInterval, true);

    // Store timer handle for cleanup
    MinionSpawnTimers.Add(SpawnTimerHandle);

    UE_LOG(LogTemp, Log, TEXT("SetupMinionSpawnTimer: Timer created for %s minions (Team: %d, Interval: %f)"),
           *MinionType, TeamIndex, SpawnInterval);

    return true;
}

// ========================================
// ROBUST TOWER CREATION - MODERN UE 5.6.1 APIS
// ========================================

AActor* FUnrealMCPMOBACommands::CreateRobustTowerActor(UWorld* World, const FString& TowerName, const FVector& Location, int32 LayerIndex, int32 Lane, int32 TowerIndex)
{
    if (!World || !IsValid(World))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustTowerActor: Invalid world"));
        return nullptr;
    }

    // STEP 1: Create tower actor with modern spawn parameters
    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = FName(*TowerName);
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
    SpawnParams.bDeferConstruction = false; // Modern UE 5.6.1 approach

    AActor* TowerActor = World->SpawnActor<AActor>(AActor::StaticClass(), Location, FRotator::ZeroRotator, SpawnParams);
    if (!TowerActor || !IsValid(TowerActor))
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to spawn tower actor: %s"), *TowerName);
        return nullptr;
    }

    // STEP 2: Create root scene component
    USceneComponent* RootComponent = NewObject<USceneComponent>(TowerActor, TEXT("TowerRoot"));
    TowerActor->SetRootComponent(RootComponent);

    // STEP 3: Add tower mesh component with modern UE 5.6.1 APIs
    UStaticMeshComponent* TowerMeshComponent = NewObject<UStaticMeshComponent>(TowerActor, TEXT("TowerMesh"));
    TowerMeshComponent->SetupAttachment(RootComponent);
    TowerActor->AddInstanceComponent(TowerMeshComponent);

    // Set tower mesh based on layer
    FString MeshPath;
    FLinearColor TowerColor;
    switch (LayerIndex)
    {
        case 0: // Planície Radiante
            MeshPath = TEXT("/Engine/BasicShapes/Cylinder.Cylinder");
            TowerColor = FLinearColor(1.0f, 0.8f, 0.2f, 1.0f); // Golden
            break;
        case 1: // Firmamento Zephyr
            MeshPath = TEXT("/Engine/BasicShapes/Cone.Cone");
            TowerColor = FLinearColor(0.2f, 0.8f, 1.0f, 1.0f); // Sky Blue
            break;
        case 2: // Abismo Umbral
            MeshPath = TEXT("/Engine/BasicShapes/Cube.Cube");
            TowerColor = FLinearColor(0.4f, 0.2f, 0.8f, 1.0f); // Purple
            break;
        default:
            MeshPath = TEXT("/Engine/BasicShapes/Cylinder.Cylinder");
            TowerColor = FLinearColor::Gray;
            break;
    }

    UStaticMesh* TowerMesh = LoadObject<UStaticMesh>(nullptr, *MeshPath);
    if (TowerMesh)
    {
        TowerMeshComponent->SetStaticMesh(TowerMesh);
        TowerMeshComponent->SetWorldScale3D(FVector(2.0f, 2.0f, 3.0f)); // Tower-like proportions

        // Create dynamic material with modern UE 5.6.1 APIs
        UMaterialInterface* BaseMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/BasicShapes/BasicShapeMaterial.BasicShapeMaterial"));
        if (BaseMaterial)
        {
            UMaterialInstanceDynamic* TowerMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, TowerMeshComponent);
            if (TowerMaterial)
            {
                TowerMaterial->SetVectorParameterValue(TEXT("Color"), TowerColor);
                TowerMeshComponent->SetMaterial(0, TowerMaterial);
            }
        }
    }

    // STEP 4: Add health component (modern UE 5.6.1 approach)
    UActorComponent* HealthComponent = NewObject<UActorComponent>(TowerActor, TEXT("TowerHealth"));
    TowerActor->AddInstanceComponent(HealthComponent);

    // STEP 5: Add attack range visualization
    USphereComponent* AttackRangeComponent = NewObject<USphereComponent>(TowerActor, TEXT("AttackRange"));
    AttackRangeComponent->SetupAttachment(RootComponent);
    AttackRangeComponent->SetSphereRadius(1000.0f + (LayerIndex * 200.0f)); // Larger range for higher layers
    AttackRangeComponent->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
    AttackRangeComponent->SetCollisionResponseToAllChannels(ECR_Ignore);
    AttackRangeComponent->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap);
    AttackRangeComponent->SetGenerateOverlapEvents(true);
    TowerActor->AddInstanceComponent(AttackRangeComponent);

    // STEP 6: Add targeting system component
    UActorComponent* TargetingComponent = NewObject<UActorComponent>(TowerActor, TEXT("TowerTargeting"));
    TowerActor->AddInstanceComponent(TargetingComponent);

    // STEP 7: Add audio component for tower sounds
    UAudioComponent* AudioComponent = NewObject<UAudioComponent>(TowerActor, TEXT("TowerAudio"));
    AudioComponent->SetupAttachment(RootComponent);
    AudioComponent->bAutoActivate = false;
    AudioComponent->SetVolumeMultiplier(0.6f);
    TowerActor->AddInstanceComponent(AudioComponent);

    // STEP 8: Configure tower tags for identification
    TowerActor->Tags.Add(FName(TEXT("AuracronTower")));
    TowerActor->Tags.Add(FName(*FString::Printf(TEXT("Layer_%d"), LayerIndex)));
    TowerActor->Tags.Add(FName(*FString::Printf(TEXT("Lane_%d"), Lane)));
    TowerActor->Tags.Add(FName(*FString::Printf(TEXT("Index_%d"), TowerIndex)));

    // STEP 9: Set tower properties based on layer
    float TowerHealth = 1000.0f + (LayerIndex * 500.0f); // More health for higher layers
    float AttackDamage = 100.0f + (LayerIndex * 50.0f);  // More damage for higher layers
    float AttackSpeed = 1.0f + (LayerIndex * 0.2f);      // Faster attack for higher layers

    TowerActor->Tags.Add(FName(*FString::Printf(TEXT("Health_%.0f"), TowerHealth)));
    TowerActor->Tags.Add(FName(*FString::Printf(TEXT("Damage_%.0f"), AttackDamage)));
    TowerActor->Tags.Add(FName(*FString::Printf(TEXT("AttackSpeed_%.1f"), AttackSpeed)));

    UE_LOG(LogTemp, Log, TEXT("CreateRobustTowerActor: Successfully created tower %s with %d components (Health: %.0f, Damage: %.0f, AttackSpeed: %.1f)"),
           *TowerName, TowerActor->GetRootComponent()->GetAttachChildren().Num() + 4, TowerHealth, AttackDamage, AttackSpeed);

    return TowerActor;
}

// ========================================
// ROBUST MINION CREATION - MODERN UE 5.6.1 APIS
// ========================================

AActor* FUnrealMCPMOBACommands::CreateRobustMinionActor(UWorld* World, const FString& MinionType, const FVector& SpawnPoint, int32 TeamIndex)
{
    if (!World || !IsValid(World))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRobustMinionActor: Invalid world"));
        return nullptr;
    }

    // STEP 1: Create minion actor with modern spawn parameters
    FString MinionName = FString::Printf(TEXT("Minion_%s_T%d_%d"), *MinionType, TeamIndex, FMath::RandRange(1000, 9999));
    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = FName(*MinionName);
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
    SpawnParams.bDeferConstruction = false; // Modern UE 5.6.1 approach

    AActor* MinionActor = World->SpawnActor<AActor>(AActor::StaticClass(), SpawnPoint, FRotator::ZeroRotator, SpawnParams);
    if (!MinionActor || !IsValid(MinionActor))
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to spawn minion actor: %s"), *MinionName);
        return nullptr;
    }

    // STEP 2: Create root scene component
    USceneComponent* RootComponent = NewObject<USceneComponent>(MinionActor, TEXT("MinionRoot"));
    MinionActor->SetRootComponent(RootComponent);

    // STEP 3: Add minion mesh component with modern UE 5.6.1 APIs
    UStaticMeshComponent* MinionMeshComponent = NewObject<UStaticMeshComponent>(MinionActor, TEXT("MinionMesh"));
    MinionMeshComponent->SetupAttachment(RootComponent);
    MinionActor->AddInstanceComponent(MinionMeshComponent);

    // Set minion mesh and properties based on type
    FString MeshPath;
    FLinearColor MinionColor;
    FVector MinionScale;
    float MinionHealth;
    float MinionDamage;
    float MinionSpeed;

    if (MinionType == TEXT("Melee"))
    {
        MeshPath = TEXT("/Engine/BasicShapes/Cube.Cube");
        MinionScale = FVector(0.8f, 0.8f, 1.2f);
        MinionHealth = 100.0f;
        MinionDamage = 25.0f;
        MinionSpeed = 300.0f;
    }
    else if (MinionType == TEXT("Ranged"))
    {
        MeshPath = TEXT("/Engine/BasicShapes/Cone.Cone");
        MinionScale = FVector(0.6f, 0.6f, 1.0f);
        MinionHealth = 75.0f;
        MinionDamage = 35.0f;
        MinionSpeed = 250.0f;
    }
    else if (MinionType == TEXT("Siege"))
    {
        MeshPath = TEXT("/Engine/BasicShapes/Cylinder.Cylinder");
        MinionScale = FVector(1.2f, 1.2f, 1.5f);
        MinionHealth = 200.0f;
        MinionDamage = 50.0f;
        MinionSpeed = 150.0f;
    }
    else // Default
    {
        MeshPath = TEXT("/Engine/BasicShapes/Sphere.Sphere");
        MinionScale = FVector(0.7f, 0.7f, 0.7f);
        MinionHealth = 80.0f;
        MinionDamage = 20.0f;
        MinionSpeed = 280.0f;
    }

    // Set team color
    MinionColor = TeamIndex == 0 ? FLinearColor::Blue : FLinearColor::Red;

    UStaticMesh* MinionMesh = LoadObject<UStaticMesh>(nullptr, *MeshPath);
    if (MinionMesh)
    {
        MinionMeshComponent->SetStaticMesh(MinionMesh);
        MinionMeshComponent->SetWorldScale3D(MinionScale);

        // Create dynamic material with modern UE 5.6.1 APIs
        UMaterialInterface* BaseMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/BasicShapes/BasicShapeMaterial.BasicShapeMaterial"));
        if (BaseMaterial)
        {
            UMaterialInstanceDynamic* MinionMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, MinionMeshComponent);
            if (MinionMaterial)
            {
                MinionMaterial->SetVectorParameterValue(TEXT("Color"), MinionColor);
                MinionMeshComponent->SetMaterial(0, MinionMaterial);
            }
        }
    }

    // STEP 4: Add collision component for minion
    UCapsuleComponent* CollisionComponent = NewObject<UCapsuleComponent>(MinionActor, TEXT("MinionCollision"));
    CollisionComponent->SetupAttachment(RootComponent);
    CollisionComponent->SetCapsuleHalfHeight(60.0f);
    CollisionComponent->SetCapsuleRadius(30.0f);
    CollisionComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
    CollisionComponent->SetCollisionResponseToAllChannels(ECR_Block);
    CollisionComponent->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap);
    MinionActor->AddInstanceComponent(CollisionComponent);

    // STEP 5: Add health component
    UActorComponent* HealthComponent = NewObject<UActorComponent>(MinionActor, TEXT("MinionHealth"));
    MinionActor->AddInstanceComponent(HealthComponent);

    // STEP 6: Add AI movement component
    UActorComponent* MovementComponent = NewObject<UActorComponent>(MinionActor, TEXT("MinionMovement"));
    MinionActor->AddInstanceComponent(MovementComponent);

    // STEP 7: Add attack component
    UActorComponent* AttackComponent = NewObject<UActorComponent>(MinionActor, TEXT("MinionAttack"));
    MinionActor->AddInstanceComponent(AttackComponent);

    // STEP 8: Configure minion tags for identification
    MinionActor->Tags.Add(FName(TEXT("AuracronMinion")));
    MinionActor->Tags.Add(FName(*FString::Printf(TEXT("Team_%d"), TeamIndex)));
    MinionActor->Tags.Add(FName(*FString::Printf(TEXT("Type_%s"), *MinionType)));
    MinionActor->Tags.Add(FName(*FString::Printf(TEXT("Health_%.0f"), MinionHealth)));
    MinionActor->Tags.Add(FName(*FString::Printf(TEXT("Damage_%.0f"), MinionDamage)));
    MinionActor->Tags.Add(FName(*FString::Printf(TEXT("Speed_%.0f"), MinionSpeed)));

    UE_LOG(LogTemp, Log, TEXT("CreateRobustMinionActor: Successfully created minion %s (Type: %s, Team: %d, Health: %.0f, Damage: %.0f, Speed: %.0f)"),
           *MinionName, *MinionType, TeamIndex, MinionHealth, MinionDamage, MinionSpeed);

    return MinionActor;
}
// CORREÇÃO ROBUSTA: Função que cria spawners reais ao invés de apenas JSONs
AActor* FUnrealMCPMOBACommands::CreateRealMinionSpawner(UWorld* World, const FString& SpawnerName, const FVector& Location, const FString& MinionType, int32 LayerIndex, int32 TeamIndex, int32 LaneIndex)
{
    if (!World || !IsValid(World))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRealMinionSpawner: Invalid world"));
        return nullptr;
    }

    // STEP 1: Criar ator spawner real
    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = FName(*SpawnerName);
    SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;

    AActor* SpawnerActor = World->SpawnActor<AActor>(AActor::StaticClass(), Location, FRotator::ZeroRotator, SpawnParams);
    if (!SpawnerActor || !IsValid(SpawnerActor))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateRealMinionSpawner: Failed to spawn spawner actor"));
        return nullptr;
    }

    SpawnerActor->SetActorLabel(SpawnerName);

    // STEP 2: Adicionar componente visual para identificar o spawner
    UStaticMeshComponent* VisualComponent = NewObject<UStaticMeshComponent>(SpawnerActor);
    if (VisualComponent)
    {
        // Usar mesh diferente baseado no tipo de minion
        FString MeshPath;
        if (MinionType.Contains(TEXT("crystal")))
        {
            MeshPath = TEXT("/Engine/BasicShapes/Cube");
        }
        else if (MinionType.Contains(TEXT("wind")))
        {
            MeshPath = TEXT("/Engine/BasicShapes/Cone");
        }
        else if (MinionType.Contains(TEXT("shadow")))
        {
            MeshPath = TEXT("/Engine/BasicShapes/Sphere");
        }
        else
        {
            MeshPath = TEXT("/Engine/BasicShapes/Cylinder");
        }

        UStaticMesh* SpawnerMesh = LoadObject<UStaticMesh>(nullptr, *MeshPath);
        if (SpawnerMesh)
        {
            VisualComponent->SetStaticMesh(SpawnerMesh);
            VisualComponent->SetWorldLocation(Location);
            
            // Escalar baseado na layer
            float Scale = 1.0f + (LayerIndex * 0.5f);
            VisualComponent->SetWorldScale3D(FVector(Scale, Scale, Scale));
            
            // Aplicar material baseado no team
            UMaterialInterface* TeamMaterial = nullptr;
            if (TeamIndex == 0)
            {
                TeamMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/EngineMaterials/DefaultMaterial"));
            }
            else
            {
                TeamMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/EngineMaterials/DefaultMaterial"));
            }
            
            if (TeamMaterial)
            {
                VisualComponent->SetMaterial(0, TeamMaterial);
            }
            
            SpawnerActor->SetRootComponent(VisualComponent);
        }
    }

    // STEP 3: Adicionar componente de collision
    UBoxComponent* CollisionComponent = NewObject<UBoxComponent>(SpawnerActor);
    if (CollisionComponent)
    {
        CollisionComponent->SetBoxExtent(FVector(100.0f, 100.0f, 100.0f));
        CollisionComponent->SetWorldLocation(Location);
        CollisionComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
        CollisionComponent->SetCollisionObjectType(ECollisionChannel::ECC_WorldStatic);
        CollisionComponent->AttachToComponent(SpawnerActor->GetRootComponent(), FAttachmentTransformRules::KeepWorldTransform);
    }

    // STEP 4: Adicionar componente de spawning logic (usando Timer Component)
    UActorComponent* SpawningLogicComponent = NewObject<UActorComponent>(SpawnerActor);
    if (SpawningLogicComponent)
    {
        SpawningLogicComponent->SetComponentTickEnabled(true);
        SpawnerActor->AddInstanceComponent(SpawningLogicComponent);
    }

    // STEP 5: Configurar propriedades customizadas do spawner
    SpawnerActor->Tags.Add(FName(TEXT("MinionSpawner")));
    SpawnerActor->Tags.Add(FName(*FString::Printf(TEXT("Layer_%d"), LayerIndex)));
    SpawnerActor->Tags.Add(FName(*FString::Printf(TEXT("Team_%d"), TeamIndex)));
    SpawnerActor->Tags.Add(FName(*FString::Printf(TEXT("Lane_%d"), LaneIndex)));
    SpawnerActor->Tags.Add(FName(*MinionType));

    UE_LOG(LogTemp, Log, TEXT("CreateRealMinionSpawner: Successfully created REAL spawner actor %s at %s"), 
           *SpawnerName, *Location.ToString());

    return SpawnerActor;
}

// Implementação duplicada removida - usando apenas a primeira implementação da função CreateRobustTowerActor