// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Commands/UnrealMCPArchitectureCommands.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeUnrealMCPArchitectureCommands() {}

// ********** Begin Cross Module References ********************************************************
COREUOBJECT_API UClass* Z_Construct_UClass_UObject();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FTransform();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UHierarchicalInstancedStaticMeshComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInterface_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USimpleConstructionScript_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USplineComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMesh_NoRegister();
UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPArchitectureCommands();
UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPArchitectureCommands_NoRegister();
UNREALMCP_API UScriptStruct* Z_Construct_UScriptStruct_FAuracronTowerConfig();
UNREALMCP_API UScriptStruct* Z_Construct_UScriptStruct_FBuildingElementConfig();
UNREALMCP_API UScriptStruct* Z_Construct_UScriptStruct_FJungleCampConfig();
UNREALMCP_API UScriptStruct* Z_Construct_UScriptStruct_FSplineStructureConfig();
UPackage* Z_Construct_UPackage__Script_UnrealMCP();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FAuracronTowerConfig **********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FAuracronTowerConfig;
class UScriptStruct* FAuracronTowerConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTowerConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FAuracronTowerConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAuracronTowerConfig, (UObject*)Z_Construct_UPackage__Script_UnrealMCP(), TEXT("AuracronTowerConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTowerConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Auracron tower structure configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auracron tower structure configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TowerName_MetaData[] = {
		{ "Category", "AuracronTowerConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerIndex_MetaData[] = {
		{ "Category", "AuracronTowerConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TeamIndex_MetaData[] = {
		{ "Category", "AuracronTowerConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TowerLocation_MetaData[] = {
		{ "Category", "AuracronTowerConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TowerScale_MetaData[] = {
		{ "Category", "AuracronTowerConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TowerRotation_MetaData[] = {
		{ "Category", "AuracronTowerConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TowerHeight_MetaData[] = {
		{ "Category", "AuracronTowerConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TowerRadius_MetaData[] = {
		{ "Category", "AuracronTowerConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TowerLevels_MetaData[] = {
		{ "Category", "AuracronTowerConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseHierarchicalInstancing_MetaData[] = {
		{ "Category", "AuracronTowerConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUsePCGGeneration_MetaData[] = {
		{ "Category", "AuracronTowerConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_TowerName;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LayerIndex;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TeamIndex;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TowerLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TowerScale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TowerRotation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TowerHeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TowerRadius;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TowerLevels;
	static void NewProp_bUseHierarchicalInstancing_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseHierarchicalInstancing;
	static void NewProp_bUsePCGGeneration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUsePCGGeneration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAuracronTowerConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::NewProp_TowerName = { "TowerName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTowerConfig, TowerName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TowerName_MetaData), NewProp_TowerName_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::NewProp_LayerIndex = { "LayerIndex", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTowerConfig, LayerIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerIndex_MetaData), NewProp_LayerIndex_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::NewProp_TeamIndex = { "TeamIndex", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTowerConfig, TeamIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TeamIndex_MetaData), NewProp_TeamIndex_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::NewProp_TowerLocation = { "TowerLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTowerConfig, TowerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TowerLocation_MetaData), NewProp_TowerLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::NewProp_TowerScale = { "TowerScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTowerConfig, TowerScale), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TowerScale_MetaData), NewProp_TowerScale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::NewProp_TowerRotation = { "TowerRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTowerConfig, TowerRotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TowerRotation_MetaData), NewProp_TowerRotation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::NewProp_TowerHeight = { "TowerHeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTowerConfig, TowerHeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TowerHeight_MetaData), NewProp_TowerHeight_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::NewProp_TowerRadius = { "TowerRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTowerConfig, TowerRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TowerRadius_MetaData), NewProp_TowerRadius_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::NewProp_TowerLevels = { "TowerLevels", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAuracronTowerConfig, TowerLevels), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TowerLevels_MetaData), NewProp_TowerLevels_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::NewProp_bUseHierarchicalInstancing_SetBit(void* Obj)
{
	((FAuracronTowerConfig*)Obj)->bUseHierarchicalInstancing = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::NewProp_bUseHierarchicalInstancing = { "bUseHierarchicalInstancing", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTowerConfig), &Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::NewProp_bUseHierarchicalInstancing_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseHierarchicalInstancing_MetaData), NewProp_bUseHierarchicalInstancing_MetaData) };
void Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::NewProp_bUsePCGGeneration_SetBit(void* Obj)
{
	((FAuracronTowerConfig*)Obj)->bUsePCGGeneration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::NewProp_bUsePCGGeneration = { "bUsePCGGeneration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAuracronTowerConfig), &Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::NewProp_bUsePCGGeneration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUsePCGGeneration_MetaData), NewProp_bUsePCGGeneration_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::NewProp_TowerName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::NewProp_LayerIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::NewProp_TeamIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::NewProp_TowerLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::NewProp_TowerScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::NewProp_TowerRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::NewProp_TowerHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::NewProp_TowerRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::NewProp_TowerLevels,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::NewProp_bUseHierarchicalInstancing,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::NewProp_bUsePCGGeneration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_UnrealMCP,
	nullptr,
	&NewStructOps,
	"AuracronTowerConfig",
	Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::PropPointers),
	sizeof(FAuracronTowerConfig),
	alignof(FAuracronTowerConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAuracronTowerConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FAuracronTowerConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FAuracronTowerConfig.InnerSingleton, Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FAuracronTowerConfig.InnerSingleton;
}
// ********** End ScriptStruct FAuracronTowerConfig ************************************************

// ********** Begin ScriptStruct FBuildingElementConfig ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FBuildingElementConfig;
class UScriptStruct* FBuildingElementConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FBuildingElementConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FBuildingElementConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FBuildingElementConfig, (UObject*)Z_Construct_UPackage__Script_UnrealMCP(), TEXT("BuildingElementConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FBuildingElementConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FBuildingElementConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Building element configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Building element configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ElementName_MetaData[] = {
		{ "Category", "BuildingElementConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ElementType_MetaData[] = {
		{ "Category", "BuildingElementConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ElementScale_MetaData[] = {
		{ "Category", "BuildingElementConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// wall, pillar, roof, foundation\n" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "wall, pillar, roof, foundation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ElementTransform_MetaData[] = {
		{ "Category", "BuildingElementConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ElementMesh_MetaData[] = {
		{ "Category", "BuildingElementConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ElementMaterial_MetaData[] = {
		{ "Category", "BuildingElementConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ElementName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ElementType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ElementScale;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ElementTransform;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ElementMesh;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ElementMaterial;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FBuildingElementConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FBuildingElementConfig_Statics::NewProp_ElementName = { "ElementName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBuildingElementConfig, ElementName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ElementName_MetaData), NewProp_ElementName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FBuildingElementConfig_Statics::NewProp_ElementType = { "ElementType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBuildingElementConfig, ElementType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ElementType_MetaData), NewProp_ElementType_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FBuildingElementConfig_Statics::NewProp_ElementScale = { "ElementScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBuildingElementConfig, ElementScale), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ElementScale_MetaData), NewProp_ElementScale_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FBuildingElementConfig_Statics::NewProp_ElementTransform = { "ElementTransform", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBuildingElementConfig, ElementTransform), Z_Construct_UScriptStruct_FTransform, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ElementTransform_MetaData), NewProp_ElementTransform_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FBuildingElementConfig_Statics::NewProp_ElementMesh = { "ElementMesh", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBuildingElementConfig, ElementMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ElementMesh_MetaData), NewProp_ElementMesh_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FBuildingElementConfig_Statics::NewProp_ElementMaterial = { "ElementMaterial", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FBuildingElementConfig, ElementMaterial), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ElementMaterial_MetaData), NewProp_ElementMaterial_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FBuildingElementConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBuildingElementConfig_Statics::NewProp_ElementName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBuildingElementConfig_Statics::NewProp_ElementType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBuildingElementConfig_Statics::NewProp_ElementScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBuildingElementConfig_Statics::NewProp_ElementTransform,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBuildingElementConfig_Statics::NewProp_ElementMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FBuildingElementConfig_Statics::NewProp_ElementMaterial,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FBuildingElementConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FBuildingElementConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_UnrealMCP,
	nullptr,
	&NewStructOps,
	"BuildingElementConfig",
	Z_Construct_UScriptStruct_FBuildingElementConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FBuildingElementConfig_Statics::PropPointers),
	sizeof(FBuildingElementConfig),
	alignof(FBuildingElementConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FBuildingElementConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FBuildingElementConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FBuildingElementConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FBuildingElementConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FBuildingElementConfig.InnerSingleton, Z_Construct_UScriptStruct_FBuildingElementConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FBuildingElementConfig.InnerSingleton;
}
// ********** End ScriptStruct FBuildingElementConfig **********************************************

// ********** Begin ScriptStruct FSplineStructureConfig ********************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FSplineStructureConfig;
class UScriptStruct* FSplineStructureConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FSplineStructureConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FSplineStructureConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSplineStructureConfig, (UObject*)Z_Construct_UPackage__Script_UnrealMCP(), TEXT("SplineStructureConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FSplineStructureConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FSplineStructureConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Spline-based structure configuration\n */" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spline-based structure configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StructureName_MetaData[] = {
		{ "Category", "SplineStructureConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SplinePoints_MetaData[] = {
		{ "Category", "SplineStructureConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StructureWidth_MetaData[] = {
		{ "Category", "SplineStructureConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StructureHeight_MetaData[] = {
		{ "Category", "SplineStructureConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SplineSegments_MetaData[] = {
		{ "Category", "SplineStructureConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bClosedLoop_MetaData[] = {
		{ "Category", "SplineStructureConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_StructureName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SplinePoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SplinePoints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StructureWidth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StructureHeight;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SplineSegments;
	static void NewProp_bClosedLoop_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bClosedLoop;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSplineStructureConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FSplineStructureConfig_Statics::NewProp_StructureName = { "StructureName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSplineStructureConfig, StructureName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StructureName_MetaData), NewProp_StructureName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSplineStructureConfig_Statics::NewProp_SplinePoints_Inner = { "SplinePoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FSplineStructureConfig_Statics::NewProp_SplinePoints = { "SplinePoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSplineStructureConfig, SplinePoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SplinePoints_MetaData), NewProp_SplinePoints_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSplineStructureConfig_Statics::NewProp_StructureWidth = { "StructureWidth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSplineStructureConfig, StructureWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StructureWidth_MetaData), NewProp_StructureWidth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSplineStructureConfig_Statics::NewProp_StructureHeight = { "StructureHeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSplineStructureConfig, StructureHeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StructureHeight_MetaData), NewProp_StructureHeight_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSplineStructureConfig_Statics::NewProp_SplineSegments = { "SplineSegments", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSplineStructureConfig, SplineSegments), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SplineSegments_MetaData), NewProp_SplineSegments_MetaData) };
void Z_Construct_UScriptStruct_FSplineStructureConfig_Statics::NewProp_bClosedLoop_SetBit(void* Obj)
{
	((FSplineStructureConfig*)Obj)->bClosedLoop = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSplineStructureConfig_Statics::NewProp_bClosedLoop = { "bClosedLoop", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSplineStructureConfig), &Z_Construct_UScriptStruct_FSplineStructureConfig_Statics::NewProp_bClosedLoop_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bClosedLoop_MetaData), NewProp_bClosedLoop_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSplineStructureConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSplineStructureConfig_Statics::NewProp_StructureName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSplineStructureConfig_Statics::NewProp_SplinePoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSplineStructureConfig_Statics::NewProp_SplinePoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSplineStructureConfig_Statics::NewProp_StructureWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSplineStructureConfig_Statics::NewProp_StructureHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSplineStructureConfig_Statics::NewProp_SplineSegments,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSplineStructureConfig_Statics::NewProp_bClosedLoop,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSplineStructureConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSplineStructureConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_UnrealMCP,
	nullptr,
	&NewStructOps,
	"SplineStructureConfig",
	Z_Construct_UScriptStruct_FSplineStructureConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSplineStructureConfig_Statics::PropPointers),
	sizeof(FSplineStructureConfig),
	alignof(FSplineStructureConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSplineStructureConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSplineStructureConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSplineStructureConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FSplineStructureConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FSplineStructureConfig.InnerSingleton, Z_Construct_UScriptStruct_FSplineStructureConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FSplineStructureConfig.InnerSingleton;
}
// ********** End ScriptStruct FSplineStructureConfig **********************************************

// ********** Begin ScriptStruct FJungleCampConfig *************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FJungleCampConfig;
class UScriptStruct* FJungleCampConfig::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FJungleCampConfig.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FJungleCampConfig.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FJungleCampConfig, (UObject*)Z_Construct_UPackage__Script_UnrealMCP(), TEXT("JungleCampConfig"));
	}
	return Z_Registration_Info_UScriptStruct_FJungleCampConfig.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FJungleCampConfig_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * Jungle Camp Configuration - Modern UE 5.6.1 PCG-based jungle generation\n */" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Jungle Camp Configuration - Modern UE 5.6.1 PCG-based jungle generation" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CampName_MetaData[] = {
		{ "Category", "JungleCampConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CampType_MetaData[] = {
		{ "Category", "JungleCampConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerIndex_MetaData[] = {
		{ "Category", "JungleCampConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// small, medium, epic, boss\n" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "small, medium, epic, boss" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CampLocation_MetaData[] = {
		{ "Category", "JungleCampConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CampRadius_MetaData[] = {
		{ "Category", "JungleCampConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VegetationDensity_MetaData[] = {
		{ "Category", "JungleCampConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TreeCount_MetaData[] = {
		{ "Category", "JungleCampConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 0.0 to 1.0\n" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "0.0 to 1.0" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BushCount_MetaData[] = {
		{ "Category", "JungleCampConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MonsterSpawnPoints_MetaData[] = {
		{ "Category", "JungleCampConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RespawnTime_MetaData[] = {
		{ "Category", "JungleCampConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUsePCGGeneration_MetaData[] = {
		{ "Category", "JungleCampConfig" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// seconds\n" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "seconds" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCreateClearingCenter_MetaData[] = {
		{ "Category", "JungleCampConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TreeMeshes_MetaData[] = {
		{ "Category", "JungleCampConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BushMeshes_MetaData[] = {
		{ "Category", "JungleCampConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayerMaterial_MetaData[] = {
		{ "Category", "JungleCampConfig" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CampName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CampType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LayerIndex;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CampLocation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CampRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VegetationDensity;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TreeCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_BushCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MonsterSpawnPoints;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RespawnTime;
	static void NewProp_bUsePCGGeneration_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUsePCGGeneration;
	static void NewProp_bCreateClearingCenter_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCreateClearingCenter;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_TreeMeshes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TreeMeshes;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_BushMeshes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_BushMeshes;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_LayerMaterial;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FJungleCampConfig>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_CampName = { "CampName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FJungleCampConfig, CampName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CampName_MetaData), NewProp_CampName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_CampType = { "CampType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FJungleCampConfig, CampType), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CampType_MetaData), NewProp_CampType_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_LayerIndex = { "LayerIndex", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FJungleCampConfig, LayerIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerIndex_MetaData), NewProp_LayerIndex_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_CampLocation = { "CampLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FJungleCampConfig, CampLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CampLocation_MetaData), NewProp_CampLocation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_CampRadius = { "CampRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FJungleCampConfig, CampRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CampRadius_MetaData), NewProp_CampRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_VegetationDensity = { "VegetationDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FJungleCampConfig, VegetationDensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VegetationDensity_MetaData), NewProp_VegetationDensity_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_TreeCount = { "TreeCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FJungleCampConfig, TreeCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TreeCount_MetaData), NewProp_TreeCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_BushCount = { "BushCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FJungleCampConfig, BushCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BushCount_MetaData), NewProp_BushCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_MonsterSpawnPoints = { "MonsterSpawnPoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FJungleCampConfig, MonsterSpawnPoints), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MonsterSpawnPoints_MetaData), NewProp_MonsterSpawnPoints_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_RespawnTime = { "RespawnTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FJungleCampConfig, RespawnTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RespawnTime_MetaData), NewProp_RespawnTime_MetaData) };
void Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_bUsePCGGeneration_SetBit(void* Obj)
{
	((FJungleCampConfig*)Obj)->bUsePCGGeneration = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_bUsePCGGeneration = { "bUsePCGGeneration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FJungleCampConfig), &Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_bUsePCGGeneration_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUsePCGGeneration_MetaData), NewProp_bUsePCGGeneration_MetaData) };
void Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_bCreateClearingCenter_SetBit(void* Obj)
{
	((FJungleCampConfig*)Obj)->bCreateClearingCenter = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_bCreateClearingCenter = { "bCreateClearingCenter", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FJungleCampConfig), &Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_bCreateClearingCenter_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCreateClearingCenter_MetaData), NewProp_bCreateClearingCenter_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_TreeMeshes_Inner = { "TreeMeshes", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_TreeMeshes = { "TreeMeshes", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FJungleCampConfig, TreeMeshes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TreeMeshes_MetaData), NewProp_TreeMeshes_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_BushMeshes_Inner = { "BushMeshes", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_BushMeshes = { "BushMeshes", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FJungleCampConfig, BushMeshes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BushMeshes_MetaData), NewProp_BushMeshes_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_LayerMaterial = { "LayerMaterial", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FJungleCampConfig, LayerMaterial), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayerMaterial_MetaData), NewProp_LayerMaterial_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FJungleCampConfig_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_CampName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_CampType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_LayerIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_CampLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_CampRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_VegetationDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_TreeCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_BushCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_MonsterSpawnPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_RespawnTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_bUsePCGGeneration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_bCreateClearingCenter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_TreeMeshes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_TreeMeshes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_BushMeshes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_BushMeshes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewProp_LayerMaterial,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FJungleCampConfig_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FJungleCampConfig_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_UnrealMCP,
	nullptr,
	&NewStructOps,
	"JungleCampConfig",
	Z_Construct_UScriptStruct_FJungleCampConfig_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FJungleCampConfig_Statics::PropPointers),
	sizeof(FJungleCampConfig),
	alignof(FJungleCampConfig),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000201),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FJungleCampConfig_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FJungleCampConfig_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FJungleCampConfig()
{
	if (!Z_Registration_Info_UScriptStruct_FJungleCampConfig.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FJungleCampConfig.InnerSingleton, Z_Construct_UScriptStruct_FJungleCampConfig_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FJungleCampConfig.InnerSingleton;
}
// ********** End ScriptStruct FJungleCampConfig ***************************************************

// ********** Begin Class UUnrealMCPArchitectureCommands *******************************************
void UUnrealMCPArchitectureCommands::StaticRegisterNativesUUnrealMCPArchitectureCommands()
{
}
FClassRegistrationInfo Z_Registration_Info_UClass_UUnrealMCPArchitectureCommands;
UClass* UUnrealMCPArchitectureCommands::GetPrivateStaticClass()
{
	using TClass = UUnrealMCPArchitectureCommands;
	if (!Z_Registration_Info_UClass_UUnrealMCPArchitectureCommands.InnerSingleton)
	{
		GetPrivateStaticClassBody(
			StaticPackage(),
			TEXT("UnrealMCPArchitectureCommands"),
			Z_Registration_Info_UClass_UUnrealMCPArchitectureCommands.InnerSingleton,
			StaticRegisterNativesUUnrealMCPArchitectureCommands,
			sizeof(TClass),
			alignof(TClass),
			TClass::StaticClassFlags,
			TClass::StaticClassCastFlags(),
			TClass::StaticConfigName(),
			(UClass::ClassConstructorType)InternalConstructor<TClass>,
			(UClass::ClassVTableHelperCtorCallerType)InternalVTableHelperCtorCaller<TClass>,
			UOBJECT_CPPCLASS_STATICFUNCTIONS_FORCLASS(TClass),
			&TClass::Super::StaticClass,
			&TClass::WithinClass::StaticClass
		);
	}
	return Z_Registration_Info_UClass_UUnrealMCPArchitectureCommands.InnerSingleton;
}
UClass* Z_Construct_UClass_UUnrealMCPArchitectureCommands_NoRegister()
{
	return UUnrealMCPArchitectureCommands::GetPrivateStaticClass();
}
struct Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "/**\n * UnrealMCP Architecture Commands - Modern UE 5.6.1 Implementation\n * Handles procedural architectural structure creation with Auracron-specific designs\n */" },
#endif
		{ "IncludePath", "Commands/UnrealMCPArchitectureCommands.h" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "UnrealMCP Architecture Commands - Modern UE 5.6.1 Implementation\nHandles procedural architectural structure creation with Auracron-specific designs" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CreatedStructures_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache for created structures\n" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache for created structures" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HierarchicalComponents_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache for hierarchical instanced components\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache for hierarchical instanced components" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SplineComponents_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache for spline components\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache for spline components" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConstructionScripts_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cache for construction scripts\n" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPArchitectureCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cache for construction scripts" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CreatedStructures_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CreatedStructures_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CreatedStructures;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_HierarchicalComponents_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_HierarchicalComponents_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_HierarchicalComponents;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SplineComponents_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SplineComponents_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_SplineComponents;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ConstructionScripts_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ConstructionScripts_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ConstructionScripts;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UUnrealMCPArchitectureCommands>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics::NewProp_CreatedStructures_ValueProp = { "CreatedStructures", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics::NewProp_CreatedStructures_Key_KeyProp = { "CreatedStructures_Key", nullptr, (EPropertyFlags)0x0100000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics::NewProp_CreatedStructures = { "CreatedStructures", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UUnrealMCPArchitectureCommands, CreatedStructures), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CreatedStructures_MetaData), NewProp_CreatedStructures_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics::NewProp_HierarchicalComponents_ValueProp = { "HierarchicalComponents", nullptr, (EPropertyFlags)0x0104000000080008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UHierarchicalInstancedStaticMeshComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics::NewProp_HierarchicalComponents_Key_KeyProp = { "HierarchicalComponents_Key", nullptr, (EPropertyFlags)0x0100000000080008, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics::NewProp_HierarchicalComponents = { "HierarchicalComponents", nullptr, (EPropertyFlags)0x0144008000000008, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UUnrealMCPArchitectureCommands, HierarchicalComponents), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HierarchicalComponents_MetaData), NewProp_HierarchicalComponents_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics::NewProp_SplineComponents_ValueProp = { "SplineComponents", nullptr, (EPropertyFlags)0x0104000000080008, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_USplineComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics::NewProp_SplineComponents_Key_KeyProp = { "SplineComponents_Key", nullptr, (EPropertyFlags)0x0100000000080008, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics::NewProp_SplineComponents = { "SplineComponents", nullptr, (EPropertyFlags)0x0144008000000008, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UUnrealMCPArchitectureCommands, SplineComponents), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SplineComponents_MetaData), NewProp_SplineComponents_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics::NewProp_ConstructionScripts_ValueProp = { "ConstructionScripts", nullptr, (EPropertyFlags)0x0104000000000000, UECodeGen_Private::EPropertyGenFlags::Object | UECodeGen_Private::EPropertyGenFlags::ObjectPtr, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_USimpleConstructionScript_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics::NewProp_ConstructionScripts_Key_KeyProp = { "ConstructionScripts_Key", nullptr, (EPropertyFlags)0x0100000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics::NewProp_ConstructionScripts = { "ConstructionScripts", nullptr, (EPropertyFlags)0x0144000000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UUnrealMCPArchitectureCommands, ConstructionScripts), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConstructionScripts_MetaData), NewProp_ConstructionScripts_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics::NewProp_CreatedStructures_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics::NewProp_CreatedStructures_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics::NewProp_CreatedStructures,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics::NewProp_HierarchicalComponents_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics::NewProp_HierarchicalComponents_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics::NewProp_HierarchicalComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics::NewProp_SplineComponents_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics::NewProp_SplineComponents_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics::NewProp_SplineComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics::NewProp_ConstructionScripts_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics::NewProp_ConstructionScripts_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics::NewProp_ConstructionScripts,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UObject,
	(UObject* (*)())Z_Construct_UPackage__Script_UnrealMCP,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics::ClassParams = {
	&UUnrealMCPArchitectureCommands::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	nullptr,
	Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	0,
	UE_ARRAY_COUNT(Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics::Class_MetaDataParams), Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UUnrealMCPArchitectureCommands()
{
	if (!Z_Registration_Info_UClass_UUnrealMCPArchitectureCommands.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UUnrealMCPArchitectureCommands.OuterSingleton, Z_Construct_UClass_UUnrealMCPArchitectureCommands_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UUnrealMCPArchitectureCommands.OuterSingleton;
}
UUnrealMCPArchitectureCommands::UUnrealMCPArchitectureCommands(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UUnrealMCPArchitectureCommands);
UUnrealMCPArchitectureCommands::~UUnrealMCPArchitectureCommands() {}
// ********** End Class UUnrealMCPArchitectureCommands *********************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPArchitectureCommands_h__Script_UnrealMCP_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAuracronTowerConfig::StaticStruct, Z_Construct_UScriptStruct_FAuracronTowerConfig_Statics::NewStructOps, TEXT("AuracronTowerConfig"), &Z_Registration_Info_UScriptStruct_FAuracronTowerConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAuracronTowerConfig), 390261085U) },
		{ FBuildingElementConfig::StaticStruct, Z_Construct_UScriptStruct_FBuildingElementConfig_Statics::NewStructOps, TEXT("BuildingElementConfig"), &Z_Registration_Info_UScriptStruct_FBuildingElementConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FBuildingElementConfig), 955177349U) },
		{ FSplineStructureConfig::StaticStruct, Z_Construct_UScriptStruct_FSplineStructureConfig_Statics::NewStructOps, TEXT("SplineStructureConfig"), &Z_Registration_Info_UScriptStruct_FSplineStructureConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSplineStructureConfig), 1218123503U) },
		{ FJungleCampConfig::StaticStruct, Z_Construct_UScriptStruct_FJungleCampConfig_Statics::NewStructOps, TEXT("JungleCampConfig"), &Z_Registration_Info_UScriptStruct_FJungleCampConfig, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FJungleCampConfig), 3839752449U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UUnrealMCPArchitectureCommands, UUnrealMCPArchitectureCommands::StaticClass, TEXT("UUnrealMCPArchitectureCommands"), &Z_Registration_Info_UClass_UUnrealMCPArchitectureCommands, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UUnrealMCPArchitectureCommands), 1481692551U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPArchitectureCommands_h__Script_UnrealMCP_2671020685(TEXT("/Script/UnrealMCP"),
	Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPArchitectureCommands_h__Script_UnrealMCP_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPArchitectureCommands_h__Script_UnrealMCP_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPArchitectureCommands_h__Script_UnrealMCP_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPArchitectureCommands_h__Script_UnrealMCP_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
