#pragma once

#include "CoreMinimal.h"
#include "Json.h"

// Modern UE 5.6.1 Material APIs
#include "Materials/Material.h"
#include "Materials/MaterialInstance.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Materials/MaterialInstanceConstant.h"
#include "Materials/MaterialParameterCollection.h"
#include "Materials/MaterialParameterCollectionInstance.h"
#include "Materials/MaterialFunction.h"
#include "Materials/MaterialFunctionInterface.h"
#include "Materials/MaterialFunctionMaterialLayer.h"
#include "Materials/MaterialLayersFunctions.h"

// Modern UE 5.6.1 Texture APIs
#include "Engine/Texture2D.h"
#include "Engine/Texture2DDynamic.h"
#include "Engine/TextureRenderTarget2D.h"
#include "TextureResource.h"

// Experimental UE 5.6.1 Nanite Material APIs - Forward declaration to avoid header issues
// Note: Nanite includes will be added in implementation file if needed
namespace Nanite { struct FMaterialAudit; }

// Engine APIs
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Materials/MaterialExpressionConstant.h"
#include "Materials/MaterialExpressionConstant3Vector.h"
#include "Materials/MaterialExpressionConstant4Vector.h"
#include "Materials/MaterialExpressionTextureSample.h"
#include "Materials/MaterialExpressionMultiply.h"
#include "Materials/MaterialExpressionAdd.h"

// Editor APIs
#include "Editor.h"
#include "EditorAssetLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "Factories/MaterialFactoryNew.h"
#include "Factories/MaterialInstanceConstantFactoryNew.h"
#include "Factories/MaterialFunctionFactoryNew.h"

#include "UnrealMCPMaterialCommands.generated.h"

// ========================================
// MODERN UE 5.6.1 MATERIAL STRUCTURES
// ========================================

/**
 * Auracron layer material configuration
 */
USTRUCT(BlueprintType)
struct UNREALMCP_API FAuracronLayerMaterialConfig
{
    GENERATED_USTRUCT_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString MaterialName;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    int32 LayerIndex = 0;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FLinearColor PrimaryColor = FLinearColor::White;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FLinearColor SecondaryColor = FLinearColor::Black;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FLinearColor AccentColor = FLinearColor::Gray;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FLinearColor TertiaryColor = FLinearColor::Black;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float Metallic = 0.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float Roughness = 0.5f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float Specular = 0.5f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float EmissiveStrength = 0.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float WeatheringIntensity = 0.3f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float DetailNormalStrength = 0.5f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float SubsurfaceScattering = 0.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float Transparency = 0.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float MagicalGlowIntensity = 0.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bUseNaniteOverride = true;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bUseMaterialLayers = true;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bCreateMaterialInstance = true;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    bool bGenerateProceduralTextures = false;

    FAuracronLayerMaterialConfig()
    {
        MaterialName = TEXT("AuracronMaterial");
        LayerIndex = 0;
        PrimaryColor = FLinearColor::White;
        SecondaryColor = FLinearColor::Black;
        AccentColor = FLinearColor::Gray;
        TertiaryColor = FLinearColor::Black;
        Metallic = 0.0f;
        Roughness = 0.5f;
        Specular = 0.5f;
        EmissiveStrength = 0.0f;
        WeatheringIntensity = 0.3f;
        DetailNormalStrength = 0.5f;
        SubsurfaceScattering = 0.0f;
        Transparency = 0.0f;
        MagicalGlowIntensity = 0.0f;
        bUseNaniteOverride = true;
        bUseMaterialLayers = true;
        bCreateMaterialInstance = true;
        bGenerateProceduralTextures = false;
    }
};

/**
 * Dynamic material parameters
 */
USTRUCT(BlueprintType)
struct UNREALMCP_API FDynamicMaterialParams
{
    GENERATED_USTRUCT_BODY()

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FString ParameterName;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    float ScalarValue = 0.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    FLinearColor VectorValue = FLinearColor::White;

    UPROPERTY(BlueprintReadWrite, EditAnywhere)
    TSoftObjectPtr<UTexture> TextureValue;

    FDynamicMaterialParams()
    {
        ParameterName = TEXT("Parameter");
        ScalarValue = 0.0f;
        VectorValue = FLinearColor::White;
        TextureValue = nullptr;
    }
};

/**
 * UnrealMCP Material Commands - Modern UE 5.6.1 Implementation
 * Handles procedural material creation with Auracron-specific themes
 */
UCLASS()
class UNREALMCP_API UUnrealMCPMaterialCommands : public UObject
{
    GENERATED_BODY()

public:
    /**
     * Handle material command routing
     * @param CommandName Name of the command to execute
     * @param Params JSON parameters for the command
     * @return JSON response with command results
     */
    TSharedPtr<FJsonObject> HandleCommand(const FString& CommandName, const TSharedPtr<FJsonObject>& Params);

    /**
     * Create layer-specific materials using modern UE 5.6.1 APIs
     * @param Params - Must include:
     *                "layer_index" - Layer index (0=Planície, 1=Firmamento, 2=Abismo)
     *                "material_name" - Name of the material
     *                "material_type" - Type of material (standard, emissive, transparent)
     * @return JSON response with the created material details
     */
    TSharedPtr<FJsonObject> HandleCreateLayerMaterials(const TSharedPtr<FJsonObject>& Params);

    /**
     * Create themed textures procedurally
     * @param Params - Must include:
     *                "texture_name" - Name of the texture
     *                "texture_type" - Type of texture (diffuse, normal, roughness)
     *                "layer_index" - Layer index for theming
     *                "resolution" - Texture resolution
     * @return JSON response with texture creation results
     */
    TSharedPtr<FJsonObject> HandleCreateThemedTextures(const TSharedPtr<FJsonObject>& Params);

    /**
     * Create dynamic materials with runtime parameters
     * @param Params - Must include:
     *                "material_name" - Name of the dynamic material
     *                "base_material" - Base material to instance from
     *                "parameters" - Array of dynamic parameters
     * @return JSON response with dynamic material creation results
     */
    TSharedPtr<FJsonObject> HandleCreateDynamicMaterials(const TSharedPtr<FJsonObject>& Params);

    /**
     * Create material functions for reusability
     * @param Params - Must include:
     *                "function_name" - Name of the material function
     *                "function_type" - Type of function (blend, noise, utility)
     *                "layer_index" - Layer index for theming
     * @return JSON response with material function creation results
     */
    TSharedPtr<FJsonObject> HandleCreateMaterialFunctions(const TSharedPtr<FJsonObject>& Params);

    /**
     * Setup material parameter collections for global control
     * @param Params - Must include:
     *                "collection_name" - Name of the parameter collection
     *                "parameters" - Array of global parameters
     *                "layer_index" - Layer index for organization
     * @return JSON response with parameter collection setup results
     */
    TSharedPtr<FJsonObject> HandleSetupMaterialParameters(const TSharedPtr<FJsonObject>& Params);

private:
    /**
     * Create robust material using modern UE 5.6.1 APIs
     * @param MaterialConfig Material configuration
     * @return Created Material
     */
    UMaterial* CreateRobustMaterial(const FAuracronLayerMaterialConfig& MaterialConfig);

    /**
     * Create material instance with Nanite support
     * @param BaseMaterial Base material to instance from
     * @param InstanceName Name of the instance
     * @param LayerIndex Layer index for theming
     * @return Created MaterialInstanceConstant
     */
    UMaterialInstanceConstant* CreateNaniteMaterialInstance(UMaterial* BaseMaterial, const FString& InstanceName, int32 LayerIndex);

    /**
     * Create procedural texture using modern APIs
     * @param TextureName Name of the texture
     * @param TextureType Type of texture
     * @param LayerIndex Layer index for theming
     * @param Resolution Texture resolution
     * @return Created Texture2D
     */
    UTexture2D* CreateProceduralTexture(const FString& TextureName, const FString& TextureType, int32 LayerIndex, int32 Resolution);

    /**
     * Create material function with layer-specific logic
     * @param FunctionName Name of the function
     * @param FunctionType Type of function
     * @param LayerIndex Layer index
     * @return Created MaterialFunction
     */
    UMaterialFunction* CreateLayerMaterialFunction(const FString& FunctionName, const FString& FunctionType, int32 LayerIndex);

    /**
     * Setup parameter collection with global parameters
     * @param CollectionName Name of the collection
     * @param LayerIndex Layer index
     * @return Created MaterialParameterCollection
     */
    UMaterialParameterCollection* SetupGlobalParameterCollection(const FString& CollectionName, int32 LayerIndex);

    /**
     * Get layer-specific color scheme
     * @param LayerIndex Layer index
     * @return Color configuration for the layer
     */
    FAuracronLayerMaterialConfig GetLayerColorScheme(int32 LayerIndex);

    /**
     * AUTOMATIC MATERIAL APPLICATION - Apply layer-specific material to actor
     * @param TargetActor Actor to apply material to
     * @param LayerIndex Layer index (0=Planície, 1=Firmamento, 2=Abismo)
     * @param ElementType Type of element (Tower, Wall, Jungle, Nexus)
     */
    void ApplyLayerMaterialToActor(AActor* TargetActor, int32 LayerIndex, const FString& ElementType);

    /**
     * Apply thematic materials to all elements in a layer
     * @param LayerIndex Layer index to apply materials to
     */
    void ApplyThematicMaterialsToAllElements(int32 LayerIndex);

private:
    // Cache for created materials
    UPROPERTY()
    TMap<FString, TObjectPtr<UMaterial>> CreatedMaterials;

    // Cache for material instances
    UPROPERTY()
    TMap<FString, TObjectPtr<UMaterialInstanceConstant>> CreatedMaterialInstances;

    // Cache for material functions
    UPROPERTY()
    TMap<FString, TObjectPtr<UMaterialFunction>> CreatedMaterialFunctions;

    // Cache for parameter collections
    UPROPERTY()
    TMap<FString, TObjectPtr<UMaterialParameterCollection>> CreatedParameterCollections;

    // Cache for textures
    UPROPERTY()
    TMap<FString, TObjectPtr<UTexture2D>> CreatedTextures;
};
