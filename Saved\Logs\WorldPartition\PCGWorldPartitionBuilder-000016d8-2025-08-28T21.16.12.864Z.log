﻿Log file open, 08/28/25 21:16:13
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=20584)
LogWindows: Enabling Tpause support
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: AURACRON
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Control listening on port 39889
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.6-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.6.1-44394996+++UE5+Release-5.6"
LogCsvProfiler: Display: Metadata set : os="Windows 11 (24H2) [10.0.26100.4946] "
LogCsvProfiler: Display: Metadata set : cpu="GenuineIntel|13th Gen Intel(R) Core(TM) i5-1345U"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" ../../../../../../Game/AURACRON/AURACRON.uproject -BaseDir="C:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/" -Unattended -RunningFromUnrealEd -AbsLog=C:/Game/AURACRON/Saved/Logs/WorldPartition/PCGWorldPartitionBuilder-000016d8-2025-08-28T21.16.12.864Z.log /Game/NewMap -run=WorldPartitionBuilderCommandlet -Builder=PCGWorldPartitionBuilder -AllowCommandletRendering -AllowSoftwareRendering -AssetGatherAll=true -PCGBuilderSettings=/Game/NewPCGBuilderSettings.NewPCGBuilderSettings""
LogCsvProfiler: Display: Metadata set : loginid="8bb1964343e8298f803f869f44351803"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.301766
LogCsvProfiler: Display: Metadata set : systemresolution.resx="1280"
LogCsvProfiler: Display: Metadata set : systemresolution.resy="720"
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: -3:00, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-E181BD6C4FC4AF23E21ABC9608A5101D
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [../../../../../../Game/AURACRON/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Audio Device Manager not initializing due to all audio being disabled. If this is not intentional, please check command line arguments for "-nosound".
LogPluginManager: Looking for build plugins target receipt
LogPluginManager: Found matching target receipt: ../../../../../../Game/AURACRON/Binaries/Win64/AURACRONEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogPluginManager: Found matching target receipt: ../../../../../../Game/AURACRON/Binaries/Win64/AURACRONEditor.target
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosInsights
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin IoStoreInsights
LogPluginManager: Mounting Engine plugin MassInsights
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin PCG
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin Cascade
LogAssetRegistry: Display: Asset registry cache read as 74.3 MiB from ../../../../../../Game/AURACRON/Intermediate/CachedAssetRegistry_0.bin.
LogConfig: Display: Loading Mac ini files took 0.12 seconds
LogPluginManager: Mounting Engine plugin Niagara
LogConfig: Display: Loading VulkanPC ini files took 0.12 seconds
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogConfig: Display: Loading Android ini files took 0.13 seconds
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin AvfMedia
LogConfig: Display: Loading IOS ini files took 0.13 seconds
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin PCGGeometryScriptInterop
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin CameraCalibrationCore
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin MLAdapter
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin AnimationData
LogConfig: Display: Loading Windows ini files took 0.15 seconds
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin GameplayInsights
LogPluginManager: Mounting Engine plugin IKRig
LogConfig: Display: Loading Unix ini files took 0.15 seconds
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin TweeningUtils
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin OodleNetwork
LogConfig: Display: Loading TVOS ini files took 0.16 seconds
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogConfig: Display: Loading Linux ini files took 0.16 seconds
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin NamingTokens
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin ProjectLauncher
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin StylusInput
LogConfig: Display: Loading VisionOS ini files took 0.05 seconds
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorDataStorageFeatures
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin GeometryDataflow
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin LandscapePatch
LogPluginManager: Mounting Engine plugin LevelSequenceNavigatorBridge
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PCGBiomeCore
LogPluginManager: Mounting Engine plugin PCGBiomeSample
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin RuntimeTelemetry
LogPluginManager: Mounting Engine plugin SequenceNavigator
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin DataRegistry
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GameplayAbilities
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin GeometryScripting
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin PropertyBindingUtils
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin CompositeCore
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Project plugin UnrealMCP
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogStudioTelemetry: Started StudioTelemetry Session
LogNFORDenoise: NFORDenoise function starting up
LogEOSShared: Loaded "C:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/EOSSDK-Win64-Shipping.dll"
LogEOSShared: FEOSSDKManager::Initialize Initializing EOSSDK Version:1.17.0-41373641
LogInit: Using libcurl 8.12.1
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_TLSAUTH_SRP
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum 256 concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogConfig: Applying CVar settings from Section [/Script/CompositeCore.CompositeCorePluginSettings] File [Engine]
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.6-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=8bb1964343e8298f803f869f44351803
LogInit: DeviceId=
LogInit: Engine Version: 5.6.1-44394996+++UE5+Release-5.6
LogInit: Compatible Engine Version: 5.6.0-43139311+++UE5+Release-5.6
LogInit: Net CL: 43139311
LogInit: OS: Windows 11 (24H2) [10.0.26100.4946] (), CPU: 13th Gen Intel(R) Core(TM) i5-1345U, GPU: Intel(R) Iris(R) Xe Graphics
LogInit: Compiled (64-bit): Jul 28 2025 20:53:34
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.6
LogInit: Command Line:  ../../../../../../Game/AURACRON/AURACRON.uproject -BaseDir="C:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/" -Unattended -RunningFromUnrealEd -AbsLog=C:/Game/AURACRON/Saved/Logs/WorldPartition/PCGWorldPartitionBuilder-000016d8-2025-08-28T21.16.12.864Z.log /Game/NewMap -run=WorldPartitionBuilderCommandlet -Builder=PCGWorldPartitionBuilder -AllowCommandletRendering -AllowSoftwareRendering -AssetGatherAll=true -PCGBuilderSettings=/Game/NewPCGBuilderSettings.NewPCGBuilderSettings
LogInit: Base Directory: C:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 37
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 207
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 17
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 11
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 121
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 56
LogDevObjectVersion:   UE5-SpecialProject (59DA5D52-1232-4948-B878-597870B8E98B): 9
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   LensFileVersion (8652A554-966A-466C-9FD7-1C6DD61B1ADB): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.AllowStaticLighting:0]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.RayTracing:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.RayTracing.RayTracingProxies.ProjectEnabled:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.08.29-00.16.13:813][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.08.29-00.16.13:813][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.08.29-00.16.13:813][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.08.29-00.16.13:813][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.08.29-00.16.13:813][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.SkylightIntensityMultiplier:1.0]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.LumenScene.DirectLighting.MaxLightsPerTile:8]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.LumenScene.DirectLighting.UpdateFactor:32]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.UpdateFactor:64]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:100]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.NumAdaptiveProbes:8]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.08.29-00.16.13:813][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.BentNormal:1]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:70]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.08.29-00.16.13:814][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.NumSamples:5]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.08.29-00.16.13:814][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.08.29-00.16.13:814][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.08.29-00.16.13:814][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:2]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:256]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:256]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.MaxSampleCount:8]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.UseExistenceMask:0]]
[2025.08.29-00.16.13:814][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.08.29-00.16.13:814][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.08.29-00.16.13:814][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.08.29-00.16.13:814][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.08.29-00.16.13:819][  0]LogRHI: Using Default RHI: D3D12
[2025.08.29-00.16.13:819][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.08.29-00.16.13:819][  0]LogRHI: Loading RHI module D3D12RHI
[2025.08.29-00.16.13:821][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.08.29-00.16.13:821][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.08.29-00.16.13:942][  0]LogD3D12RHI: Intel Extensions Framework not supported by driver. Please check if a driver update is available.
[2025.08.29-00.16.13:962][  0]LogD3D12RHI: Found D3D12 adapter 0: Intel(R) Iris(R) Xe Graphics (VendorId: 8086, DeviceId: a7a1, SubSysId: c001028, Revision: 0004
[2025.08.29-00.16.13:962][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.7, binding tier 3, wave ops supported, atomic64 unsupported
[2025.08.29-00.16.13:962][  0]LogD3D12RHI:   Adapter has 128MB of dedicated video memory, 0MB of dedicated system memory, and 16226MB of shared system memory, 1 output[s], UMA:true
[2025.08.29-00.16.13:963][  0]LogD3D12RHI:   Driver Version: 32.0.101.6556 (internal:32.0.101.6556, unified:101.6556)
[2025.08.29-00.16.13:963][  0]LogD3D12RHI:      Driver Date: 1-23-2025
[2025.08.29-00.16.13:972][  0]LogD3D12RHI: Found D3D12 adapter 1: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.08.29-00.16.13:972][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.08.29-00.16.13:972][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 16226MB of shared system memory, 0 output[s], UMA:true
[2025.08.29-00.16.13:972][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.08.29-00.16.13:972][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.08.29-00.16.13:972][  0]LogD3D12RHI: Adapter only supports up to Feature Level 'SM5', requested Feature Level was 'SM6'
[2025.08.29-00.16.13:972][  0]LogRHI: RHI D3D12 with Feature Level SM6 is not supported on your system, attempting to fall back to RHI D3D11 with Feature Level SM5
[2025.08.29-00.16.13:972][  0]LogRHI: Loading RHI module D3D11RHI
[2025.08.29-00.16.13:973][  0]LogRHI: Checking if RHI D3D11 with Feature Level SM5 is supported by your system.
[2025.08.29-00.16.13:973][  0]LogD3D11RHI: D3D11 min allowed feature level: 11_0
[2025.08.29-00.16.13:973][  0]LogD3D11RHI: D3D11 max allowed feature level: 11_1
[2025.08.29-00.16.13:973][  0]LogD3D11RHI: D3D11 adapters:
[2025.08.29-00.16.13:974][  0]LogD3D11RHI: Testing D3D11 Adapter 0:
[2025.08.29-00.16.13:974][  0]LogD3D11RHI:     Description : Intel(R) Iris(R) Xe Graphics
[2025.08.29-00.16.13:974][  0]LogD3D11RHI:     VendorId    : 8086
[2025.08.29-00.16.13:974][  0]LogD3D11RHI:     DeviceId    : a7a1
[2025.08.29-00.16.13:974][  0]LogD3D11RHI:     SubSysId    : c001028
[2025.08.29-00.16.13:974][  0]LogD3D11RHI:     Revision    : 0004
[2025.08.29-00.16.13:974][  0]LogD3D11RHI:     DedicatedVideoMemory : 134217728 bytes
[2025.08.29-00.16.13:974][  0]LogD3D11RHI:     DedicatedSystemMemory : 0 bytes
[2025.08.29-00.16.13:974][  0]LogD3D11RHI:     SharedSystemMemory : 17014562816 bytes
[2025.08.29-00.16.13:974][  0]LogD3D11RHI:     AdapterLuid : 0 85693
[2025.08.29-00.16.14:205][  0]LogD3D11RHI:    0. 'Intel(R) Iris(R) Xe Graphics' (Feature Level 11_1)
[2025.08.29-00.16.14:205][  0]LogD3D11RHI:       128/0/16226 MB DedicatedVideo/DedicatedSystem/SharedSystem, Outputs:1, VendorId:0x8086 UMA:true
[2025.08.29-00.16.14:205][  0]LogD3D11RHI: Testing D3D11 Adapter 1:
[2025.08.29-00.16.14:205][  0]LogD3D11RHI:     Description : Microsoft Basic Render Driver
[2025.08.29-00.16.14:205][  0]LogD3D11RHI:     VendorId    : 1414
[2025.08.29-00.16.14:205][  0]LogD3D11RHI:     DeviceId    : 008c
[2025.08.29-00.16.14:205][  0]LogD3D11RHI:     SubSysId    : 0000
[2025.08.29-00.16.14:205][  0]LogD3D11RHI:     Revision    : 0000
[2025.08.29-00.16.14:205][  0]LogD3D11RHI:     DedicatedVideoMemory : 0 bytes
[2025.08.29-00.16.14:205][  0]LogD3D11RHI:     DedicatedSystemMemory : 0 bytes
[2025.08.29-00.16.14:205][  0]LogD3D11RHI:     SharedSystemMemory : 17014562816 bytes
[2025.08.29-00.16.14:205][  0]LogD3D11RHI:     AdapterLuid : 0 86560
[2025.08.29-00.16.14:212][  0]LogD3D11RHI:    1. 'Microsoft Basic Render Driver' (Feature Level 11_1)
[2025.08.29-00.16.14:212][  0]LogD3D11RHI:       0/0/16226 MB DedicatedVideo/DedicatedSystem/SharedSystem, Outputs:0, VendorId:0x1414 UMA:true
[2025.08.29-00.16.14:212][  0]LogD3D11RHI: Chosen D3D11 Adapter:
[2025.08.29-00.16.14:212][  0]LogD3D11RHI:     Description : Intel(R) Iris(R) Xe Graphics
[2025.08.29-00.16.14:212][  0]LogD3D11RHI:     VendorId    : 8086
[2025.08.29-00.16.14:212][  0]LogD3D11RHI:     DeviceId    : a7a1
[2025.08.29-00.16.14:212][  0]LogD3D11RHI:     SubSysId    : c001028
[2025.08.29-00.16.14:212][  0]LogD3D11RHI:     Revision    : 0004
[2025.08.29-00.16.14:212][  0]LogD3D11RHI:     DedicatedVideoMemory : 134217728 bytes
[2025.08.29-00.16.14:212][  0]LogD3D11RHI:     DedicatedSystemMemory : 0 bytes
[2025.08.29-00.16.14:212][  0]LogD3D11RHI:     SharedSystemMemory : 17014562816 bytes
[2025.08.29-00.16.14:212][  0]LogD3D11RHI:     AdapterLuid : 0 85693
[2025.08.29-00.16.14:212][  0]LogD3D11RHI: Integrated GPU (iGPU): true
[2025.08.29-00.16.14:212][  0]LogRHI: RHI D3D11 with Feature Level SM5 is supported and will be used.
[2025.08.29-00.16.14:212][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.08.29-00.16.14:212][  0]LogHAL: Display: Platform has ~ 32 GB [34029125632 / 34359738368 / 32], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.08.29-00.16.14:213][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.08.29-00.16.14:213][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.29-00.16.14:213][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.08.29-00.16.14:213][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.08.29-00.16.14:213][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.08.29-00.16.14:213][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.08.29-00.16.14:213][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.08.29-00.16.14:213][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.08.29-00.16.14:213][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.08.29-00.16.14:213][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.08.29-00.16.14:213][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.08.29-00.16.14:213][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.08.29-00.16.14:213][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [C:/Game/AURACRON/Saved/Config/WindowsEditor/Editor.ini]
[2025.08.29-00.16.14:213][  0]LogInit: Computer: TKT
[2025.08.29-00.16.14:213][  0]LogInit: User: tktca
[2025.08.29-00.16.14:213][  0]LogInit: CPU Page size=4096, Cores=10
[2025.08.29-00.16.14:213][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.08.29-00.16.14:213][  0]LogMemory: Process is running as part of a Windows Job with separate resource limits
[2025.08.29-00.16.14:213][  0]LogMemory: Memory total: Physical=31.7GB (32GB approx) Virtual=37.6GB
[2025.08.29-00.16.14:213][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.08.29-00.16.14:213][  0]LogMemory: Process Physical Memory: 646.79 MB used, 709.57 MB peak
[2025.08.29-00.16.14:213][  0]LogMemory: Process Virtual Memory: 640.62 MB used, 684.11 MB peak
[2025.08.29-00.16.14:213][  0]LogMemory: Physical Memory: 26802.48 MB used,  5650.22 MB free, 32452.70 MB total
[2025.08.29-00.16.14:213][  0]LogMemory: Virtual Memory: 37877.12 MB used,  596.58 MB free, 38473.70 MB total
[2025.08.29-00.16.14:213][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.08.29-00.16.14:216][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.08.29-00.16.14:219][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.08.29-00.16.14:219][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.08.29-00.16.14:219][  0]LogInit: Using OS detected language (pt-BR).
[2025.08.29-00.16.14:219][  0]LogInit: Using OS detected locale (pt-BR).
[2025.08.29-00.16.14:553][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/Editor/pt/Editor.locres' could not be opened for reading!
[2025.08.29-00.16.14:553][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/EditorTutorials/pt/EditorTutorials.locres' could not be opened for reading!
[2025.08.29-00.16.14:553][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/Keywords/pt/Keywords.locres' could not be opened for reading!
[2025.08.29-00.16.14:553][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/Category/pt/Category.locres' could not be opened for reading!
[2025.08.29-00.16.14:553][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/ToolTips/pt/ToolTips.locres' could not be opened for reading!
[2025.08.29-00.16.14:553][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/PropertyNames/pt/PropertyNames.locres' could not be opened for reading!
[2025.08.29-00.16.14:553][  0]LogTextLocalizationResource: LocRes '../../../Engine/Content/Localization/Engine/pt/Engine.locres' could not be opened for reading!
[2025.08.29-00.16.14:553][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/MetaHuman/MetaHumanSDK/Content/Localization/MetaHumanSDK/pt/MetaHumanSDK.locres' could not be opened for reading!
[2025.08.29-00.16.14:553][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/Online/OnlineSubsystem/Content/Localization/OnlineSubsystem/pt/OnlineSubsystem.locres' could not be opened for reading!
[2025.08.29-00.16.14:553][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/Online/OnlineSubsystemUtils/Content/Localization/OnlineSubsystemUtils/pt/OnlineSubsystemUtils.locres' could not be opened for reading!
[2025.08.29-00.16.14:553][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/Online/Android/OnlineSubsystemGooglePlay/Content/Localization/OnlineSubsystemGooglePlay/pt/OnlineSubsystemGooglePlay.locres' could not be opened for reading!
[2025.08.29-00.16.14:553][  0]LogTextLocalizationResource: LocRes '../../../Engine/Plugins/Online/IOS/OnlineSubsystemIOS/Content/Localization/OnlineSubsystemIOS/pt/OnlineSubsystemIOS.locres' could not be opened for reading!
[2025.08.29-00.16.14:645][  0]LogObj: Display: Attempting to load config data for Default__SlateThemeManager before the Class has been constructed/registered/linked (likely during module loading or early startup). This will result in the load silently failing and should be fixed.
[2025.08.29-00.16.14:764][  0]LogRHI: Using Default RHI: D3D12
[2025.08.29-00.16.14:764][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.08.29-00.16.14:764][  0]LogRHI: Loading RHI module D3D12RHI
[2025.08.29-00.16.14:764][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.08.29-00.16.14:764][  0]LogD3D12RHI: Adapter only supports up to Feature Level 'SM5', requested Feature Level was 'SM6'
[2025.08.29-00.16.14:764][  0]LogRHI: RHI D3D12 with Feature Level SM6 is not supported on your system, attempting to fall back to RHI D3D11 with Feature Level SM5
[2025.08.29-00.16.14:764][  0]LogRHI: Loading RHI module D3D11RHI
[2025.08.29-00.16.14:764][  0]LogRHI: Checking if RHI D3D11 with Feature Level SM5 is supported by your system.
[2025.08.29-00.16.14:764][  0]LogRHI: RHI D3D11 with Feature Level SM5 is supported and will be used.
[2025.08.29-00.16.14:766][  0]LogWindows: Attached monitors:
[2025.08.29-00.16.14:766][  0]LogWindows:     resolution: 1536x864, work area: (0, 0) -> (1536, 816), device: '\\.\DISPLAY1' [PRIMARY]
[2025.08.29-00.16.14:766][  0]LogWindows: Found 1 attached monitors.
[2025.08.29-00.16.14:766][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.08.29-00.16.14:766][  0]LogRHI: RHI Adapter Info:
[2025.08.29-00.16.14:766][  0]LogRHI:             Name: Intel(R) Iris(R) Xe Graphics
[2025.08.29-00.16.14:766][  0]LogRHI:   Driver Version: 32.0.101.6556 (internal:32.0.101.6556, unified:101.6556)
[2025.08.29-00.16.14:766][  0]LogRHI:      Driver Date: 1-23-2025
[2025.08.29-00.16.14:766][  0]LogD3D11RHI: Creating new Direct3DDevice
[2025.08.29-00.16.14:766][  0]LogD3D11RHI:     GPU DeviceId: 0xa7a1 (for the marketing name, search the web for "GPU Device Id")
[2025.08.29-00.16.14:766][  0]LogRHI: Texture pool is 1523 MB (70% of 2176 MB)
[2025.08.29-00.16.14:766][  0]LogNvidiaAftermath: Nvidia Aftermath is disabled in D3D11 due to instability issues.
[2025.08.29-00.16.14:766][  0]LogD3D11RHI: Creating D3DDevice using adapter:
[2025.08.29-00.16.14:766][  0]LogD3D11RHI:     Description : Intel(R) Iris(R) Xe Graphics
[2025.08.29-00.16.14:766][  0]LogD3D11RHI:     VendorId    : 8086
[2025.08.29-00.16.14:766][  0]LogD3D11RHI:     DeviceId    : a7a1
[2025.08.29-00.16.14:766][  0]LogD3D11RHI:     SubSysId    : c001028
[2025.08.29-00.16.14:766][  0]LogD3D11RHI:     Revision    : 0004
[2025.08.29-00.16.14:766][  0]LogD3D11RHI:     DedicatedVideoMemory : 134217728 bytes
[2025.08.29-00.16.14:766][  0]LogD3D11RHI:     DedicatedSystemMemory : 0 bytes
[2025.08.29-00.16.14:766][  0]LogD3D11RHI:     SharedSystemMemory : 17014562816 bytes
[2025.08.29-00.16.14:766][  0]LogD3D11RHI:     AdapterLuid : 0 85693
[2025.08.29-00.16.14:965][  0]LogNvidiaAftermath: Aftermath is not loaded.
[2025.08.29-00.16.14:993][  0]LogD3D11RHI: Intel Extensions loaded requested version for UAVOverlap: 1.1.0
[2025.08.29-00.16.14:993][  0]LogD3D11RHI: Intel Extensions loaded requested version Atomics Version: 3.4.1
[2025.08.29-00.16.14:993][  0]LogD3D11RHI: Intel Extensions Framework enabled
[2025.08.29-00.16.14:993][  0]LogD3D11RHI: RHI has support for 64 bit atomics
[2025.08.29-00.16.14:994][  0]LogD3D11RHI: Async texture creation enabled
[2025.08.29-00.16.14:994][  0]LogD3D11RHI: D3D11_MAP_WRITE_NO_OVERWRITE for dynamic buffer SRVs is supported
[2025.08.29-00.16.14:994][  0]LogD3D11RHI: Array index from any shader is supported
[2025.08.29-00.16.15:010][  0]LogVRS: Current RHI does not support Variable Rate Shading
[2025.08.29-00.16.15:013][  0]LogCsvProfiler: Display: Metadata set : verbatimrhiname="D3D11"
[2025.08.29-00.16.15:013][  0]LogCsvProfiler: Display: Metadata set : rhiname="D3D11"
[2025.08.29-00.16.15:014][  0]LogCsvProfiler: Display: Metadata set : rhifeaturelevel="SM5"
[2025.08.29-00.16.15:014][  0]LogCsvProfiler: Display: Metadata set : shaderplatform="PCD3D_SM5"
[2025.08.29-00.16.15:014][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.08.29-00.16.15:021][  0]LogTextureFormatASTC: Display: ASTCEnc version 5.0.1 library loaded
[2025.08.29-00.16.15:021][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.08.29-00.16.15:021][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.08.29-00.16.15:021][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.08.29-00.16.15:021][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.08.29-00.16.15:021][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.08.29-00.16.15:021][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.13
[2025.08.29-00.16.15:021][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.13.dll
[2025.08.29-00.16.15:021][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.08.29-00.16.15:021][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.08.29-00.16.15:052][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.08.29-00.16.15:052][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.08.29-00.16.15:052][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.08.29-00.16.15:052][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.08.29-00.16.15:052][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_OpenXR'
[2025.08.29-00.16.15:052][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.08.29-00.16.15:052][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.08.29-00.16.15:052][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.08.29-00.16.15:052][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.08.29-00.16.15:052][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_OpenXRClient'
[2025.08.29-00.16.15:052][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.08.29-00.16.15:052][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.08.29-00.16.15:065][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.08.29-00.16.15:065][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.08.29-00.16.15:078][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.08.29-00.16.15:078][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.08.29-00.16.15:078][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.08.29-00.16.15:078][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.08.29-00.16.15:091][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.08.29-00.16.15:091][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.08.29-00.16.15:091][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.08.29-00.16.15:091][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.08.29-00.16.15:105][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.08.29-00.16.15:105][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.08.29-00.16.15:119][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.08.29-00.16.15:119][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.08.29-00.16.15:119][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.08.29-00.16.15:119][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.08.29-00.16.15:119][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.08.29-00.16.15:123][  0]LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
[2025.08.29-00.16.15:155][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL_ES3_1_IOS from hinted modules, loading all potential format modules to find it
[2025.08.29-00.16.15:161][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.08.29-00.16.15:161][  0]LogTargetPlatformManager:   SF_METAL_ES3_1_IOS
[2025.08.29-00.16.15:161][  0]LogTargetPlatformManager:   SF_METAL_SM5_IOS
[2025.08.29-00.16.15:161][  0]LogTargetPlatformManager:   SF_METAL_ES3_1_TVOS
[2025.08.29-00.16.15:161][  0]LogTargetPlatformManager:   SF_METAL_SM5_TVOS
[2025.08.29-00.16.15:161][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.08.29-00.16.15:161][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.08.29-00.16.15:161][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.08.29-00.16.15:161][  0]LogTargetPlatformManager:   SF_METAL_ES3_1
[2025.08.29-00.16.15:161][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.08.29-00.16.15:161][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.08.29-00.16.15:161][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.08.29-00.16.15:161][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.08.29-00.16.15:161][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.08.29-00.16.15:161][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.08.29-00.16.15:161][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.08.29-00.16.15:161][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.08.29-00.16.15:161][  0]LogTargetPlatformManager:   VVM_1_0
[2025.08.29-00.16.15:161][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.08.29-00.16.15:161][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.08.29-00.16.15:161][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.08.29-00.16.15:161][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.08.29-00.16.15:161][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.08.29-00.16.15:161][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.08.29-00.16.15:161][  0]LogRendererCore: Ray tracing is disabled. Reason: not supported by current RHI.
[2025.08.29-00.16.15:164][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.08.29-00.16.15:164][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file ../../../../../../Game/AURACRON/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.08.29-00.16.15:164][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.08.29-00.16.15:164][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file ../../../../../../Game/AURACRON/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.08.29-00.16.15:164][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.08.29-00.16.15:371][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1350 MiB)
[2025.08.29-00.16.15:371][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.08.29-00.16.15:371][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.08.29-00.16.15:373][  0]LogZenServiceInstance: Found subprocess environment variable UE-ZenSubprocessDataPath=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.08.29-00.16.15:374][  0]LogZenServiceInstance: InTree version at 'C:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
[2025.08.29-00.16.15:374][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
[2025.08.29-00.16.15:374][  0]LogZenServiceInstance: Found existing instance running on port 8558 matching our settings, no actions needed
[2025.08.29-00.16.15:781][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.08.29-00.16.15:781][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.407 seconds
[2025.08.29-00.16.15:782][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.08.29-00.16.15:793][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.01 seconds.
[2025.08.29-00.16.15:793][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.02ms. RandomReadSpeed=880.22MBs, RandomWriteSpeed=111.22MBs. Assigned SpeedClass 'Local'
[2025.08.29-00.16.15:795][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.08.29-00.16.15:796][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.08.29-00.16.15:796][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.08.29-00.16.15:796][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.08.29-00.16.15:796][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.08.29-00.16.15:796][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.08.29-00.16.15:796][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.08.29-00.16.15:797][  0]LogShaderCompilers: Guid format shader working directory is 14 characters bigger than the processId version (../../../../../../Game/AURACRON/Intermediate/Shaders/WorkingDirectory/31384/).
[2025.08.29-00.16.15:797][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/A9CA878543BE8C7F4BEF0AB6EA46732C/'.
[2025.08.29-00.16.15:798][  0]LogXGEController: Cannot use XGE Controller as Incredibuild is not installed on this machine.
[2025.08.29-00.16.15:798][  0]LogUbaHorde: Display: UBA/Horde Configuration [Uba.Provider.Horde]: Not Enabled
[2025.08.29-00.16.15:798][  0]LogShaderCompilers: Display: Using 9 local workers for shader compilation
[2025.08.29-00.16.15:802][  0]LogShaderCompilers: Display: Compiling shader autogen file: ../../../../../../Game/AURACRON/Intermediate/ShaderAutogen/PCD3D_SM5/AutogenShaderHeaders.ush
[2025.08.29-00.16.15:802][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.08.29-00.16.17:658][  0]LogAssetRegistry: FAssetRegistry took 0.0038 seconds to start up
[2025.08.29-00.16.17:659][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.08.29-00.16.17:660][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.000s loading caches ../../../../../../Game/AURACRON/Intermediate/CachedAssetRegistry_*.bin.
[2025.08.29-00.16.18:028][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.08.29-00.16.18:028][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.08.29-00.16.18:068][  0]LogDeviceProfileManager: Active device profile: [000002454F5EC580][000002453DEAC800 66] WindowsEditor
[2025.08.29-00.16.18:068][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.08.29-00.16.18:072][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.08.29-00.16.18:075][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: Final (cook).
[2025.08.29-00.16.18:075][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.08.29-00.16.18:075][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.08.29-00.16.18:095][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-00.16.18:096][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness because of a recursive sync load
[2025.08.29-00.16.18:096][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-00.16.18:096][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-00.16.18:097][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec because of a recursive sync load
[2025.08.29-00.16.18:097][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-00.16.18:097][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/WorldGridMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-00.16.18:098][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/T_Default_Material_Grid_M because of a recursive sync load
[2025.08.29-00.16.18:098][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/T_Default_Material_Grid_N because of a recursive sync load
[2025.08.29-00.16.18:100][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions01/Opacity/CameraDepthFade because of a recursive sync load
[2025.08.29-00.16.18:101][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-00.16.18:101][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-00.16.18:140][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-00.16.18:140][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultDiffuse because of a recursive sync load
[2025.08.29-00.16.18:140][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components because of a recursive sync load
[2025.08.29-00.16.18:140][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultPostProcessMaterial because of a recursive sync load
[2025.08.29-00.16.18:140][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-00.16.18:140][  0]LogMaterial: Display: Missing cached shadermap for DefaultPostProcessMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 65b445eaf13188b3ee79ed127cbd589d26b5a690), compiling. 
[2025.08.29-00.16.18:872][  0]LogShaderCompilers: Error: ShaderCompileWorker terminated unexpectedly, return code -1073741819! Falling back to directly compiling which will be very slow.  Thread 0.
[2025.08.29-00.16.18:872][  0]LogShaderCompilers: Error: SCW 2 Queued Jobs, Unknown number of processed jobs!
[2025.08.29-00.16.18:872][  0]LogShaderCompilers: Error: Job 0 [Single] Failed: DefaultPostProcessMaterial_532eb5880e18ce30/Default/TGPUSkinVertexFactoryDefault/TVirtualTextureVSWorldHeight/0:/Engine/Private/VirtualTextureMaterial.usf|MainVS VF 'TGPUSkinVertexFactoryDefault' Type 'TVirtualTextureVSWorldHeight' '/Engine/Private/VirtualTextureMaterial.usf' Entry 'MainVS' Permutation 0 
[2025.08.29-00.16.18:872][  0]LogShaderCompilers: Error: Job 1 [Single] Failed: DefaultPostProcessMaterial_532eb5880e18ce30/Default/TGPUSkinVertexFactoryDefault/TVirtualTexturePSBaseColor/0:/Engine/Private/VirtualTextureMaterial.usf|MainPS VF 'TGPUSkinVertexFactoryDefault' Type 'TVirtualTexturePSBaseColor' '/Engine/Private/VirtualTextureMaterial.usf' Entry 'MainPS' Permutation 0 
[2025.08.29-00.16.27:439][  0]LogShaderCompilers: Display: Retry shader compiling through workers.
[2025.08.29-00.16.27:788][  0]LogShaderCompilers: Error: ShaderCompileWorker terminated unexpectedly, return code -1073741819! Falling back to directly compiling which will be very slow.  Thread 2.
[2025.08.29-00.16.27:788][  0]LogShaderCompilers: Error: SCW 2 Queued Jobs, Unknown number of processed jobs!
[2025.08.29-00.16.27:788][  0]LogShaderCompilers: Error: Job 0 [Single] Failed: DefaultPostProcessMaterial_532eb5880e18ce30/Default/FLocalVertexFactory/TVirtualTexturePSBaseColorNormalSpecular/0:/Engine/Private/VirtualTextureMaterial.usf|MainPS VF 'FLocalVertexFactory' Type 'TVirtualTexturePSBaseColorNormalSpecular' '/Engine/Private/VirtualTextureMaterial.usf' Entry 'MainPS' Permutation 0 
[2025.08.29-00.16.27:788][  0]LogShaderCompilers: Error: Job 1 [Single] Failed: DefaultPostProcessMaterial_532eb5880e18ce30/Default/FLocalVertexFactory/TVirtualTexturePSDisplacement/0:/Engine/Private/VirtualTextureMaterial.usf|MainPS VF 'FLocalVertexFactory' Type 'TVirtualTexturePSDisplacement' '/Engine/Private/VirtualTextureMaterial.usf' Entry 'MainPS' Permutation 0 
[2025.08.29-00.16.34:700][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultLightFunctionMaterial because of a recursive sync load
[2025.08.29-00.16.34:700][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-00.16.34:715][  0]LogMaterial: Display: Missing cached shadermap for DefaultLightFunctionMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 39bf7a00c6f8a65350b2e2c5e28287205a2c7184), compiling. 
[2025.08.29-00.16.51:849][  0]LogStreaming: Display: Package /Engine/EngineMaterials/WorldGridMaterial is adding a dynamic import to package /Engine/EngineMaterials/DefaultDeferredDecalMaterial because of a recursive sync load
[2025.08.29-00.16.51:850][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-00.16.51:864][  0]LogMaterial: Display: Missing cached shadermap for DefaultDeferredDecalMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 5a13193886b09a4b38393e705ffe74add2966189), compiling. 
[2025.08.29-00.17.25:909][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultTextMaterialOpaque is adding a dynamic import to package /Engine/EngineMaterials/WorldGridMaterial because of a recursive sync load
[2025.08.29-00.17.25:909][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/WorldGridMaterial (state: DeferredPostLoad) recursively from another package /Engine/EngineMaterials/DefaultTextMaterialOpaque (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.29-00.17.25:968][  0]LogMaterial: Display: Missing cached shadermap for WorldGridMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 07b76072bcaa77b8a8657aa2300b1a93a8c07e8d), compiling. Is special engine material.
[2025.08.29-00.18.15:797][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.08.29-00.21.38:267][  0]LogMaterial: Display: Missing cached shadermap for DefaultTextMaterialOpaque in PCD3D_SM5, Default, SM5, Editor (DDC key hash: fb0909746fa59e69da912872e280d841f9ca2a25), compiling. 
[2025.08.29-00.21.38:304][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.08.29-00.21.38:304][  0]LogMeshReduction: Display: Using SkeletalMeshReduction for automatic skeletal mesh reduction
[2025.08.29-00.21.38:304][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.08.29-00.21.38:304][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.08.29-00.21.38:304][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.08.29-00.21.38:351][  0]LogStaticMesh: Display: Construindo malha estática Sphere (Estimativa de memória necessária: 0.756426 MB)...
[2025.08.29-00.21.38:373][  0]LogStaticMesh: Built static mesh [0.03s] /Engine/EngineMeshes/Sphere.Sphere
[2025.08.29-00.21.38:429][  0]LogMaterial: Display: Missing cached shadermap for Main in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 1a423a24803ba9f4b686d55a954fc5fff17ea088), compiling. 
[2025.08.29-00.21.38:435][  0]LogMaterial: Display: Missing cached shadermap for Xray in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 5e894d14d8a542d3feda39e458aeba6867a38304), compiling. 
[2025.08.29-00.21.38:440][  0]LogStaticMesh: Display: Construindo malha estática SM_Sequencer_Node (Estimativa de memória necessária: 0.08356 MB)...
[2025.08.29-00.21.38:460][  0]LogStaticMesh: Built static mesh [0.02s] /Engine/VREditor/TransformGizmo/SM_Sequencer_Node.SM_Sequencer_Node
[2025.08.29-00.21.38:480][  0]LogMaterial: Display: Missing cached shadermap for M_VolumeRenderSphereTracePP in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 7b61e262bddbeae1d8ef0fe304be5fc05f346e10), compiling. 
[2025.08.29-00.21.38:494][  0]LogMaterial: Display: Missing cached shadermap for CineMat in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 401897ac18335445d4bf4bf2bc9f3f15fb4046bb), compiling. 
[2025.08.29-00.21.38:498][  0]LogStaticMesh: Display: Construindo malha estática SM_CraneRig_Base (Estimativa de memória necessária: 1.522692 MB)...
[2025.08.29-00.21.38:518][  0]LogStaticMesh: Built static mesh [0.02s] /Engine/EditorMeshes/Camera/SM_CraneRig_Base.SM_CraneRig_Base
[2025.08.29-00.21.38:526][  0]LogStaticMesh: Display: Construindo malha estática SM_CraneRig_Arm (Estimativa de memória necessária: 0.0249 MB)...
[2025.08.29-00.21.38:534][  0]LogStaticMesh: Built static mesh [0.01s] /Engine/EditorMeshes/Camera/SM_CraneRig_Arm.SM_CraneRig_Arm
[2025.08.29-00.21.38:540][  0]LogStaticMesh: Display: Construindo malha estática SM_CraneRig_Mount (Estimativa de memória necessária: 0.292356 MB)...
[2025.08.29-00.21.38:550][  0]LogStaticMesh: Built static mesh [0.01s] /Engine/EditorMeshes/Camera/SM_CraneRig_Mount.SM_CraneRig_Mount
[2025.08.29-00.21.38:554][  0]LogStaticMesh: Display: Construindo malha estática SM_CraneRig_Body (Estimativa de memória necessária: 1.454466 MB)...
[2025.08.29-00.21.38:578][  0]LogStaticMesh: Built static mesh [0.03s] /Engine/EditorMeshes/Camera/SM_CraneRig_Body.SM_CraneRig_Body
[2025.08.29-00.21.38:586][  0]LogStaticMesh: Display: Construindo malha estática SM_RailRig_Track (Estimativa de memória necessária: 2.117645 MB)...
[2025.08.29-00.21.38:607][  0]LogStaticMesh: Built static mesh [0.02s] /Engine/EditorMeshes/Camera/SM_RailRig_Track.SM_RailRig_Track
[2025.08.29-00.21.38:615][  0]LogStaticMesh: Display: Construindo malha estática SM_RailRig_Mount (Estimativa de memória necessária: 2.380135 MB)...
[2025.08.29-00.21.38:667][  0]LogStaticMesh: Built static mesh [0.05s] /Engine/EditorMeshes/Camera/SM_RailRig_Mount.SM_RailRig_Mount
[2025.08.29-00.21.38:692][  0]LogMaterial: Display: Missing cached shadermap for SplineEditorMeshMat in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 9e885668644069fcdce2f082ed7ff236f1fd8cf3), compiling. 
[2025.08.29-00.21.38:696][  0]LogStaticMesh: Display: Construindo malha estática SplineEditorMesh (Estimativa de memória necessária: 0.094715 MB)...
[2025.08.29-00.21.38:699][  0]LogStaticMesh: Built static mesh [0.00s] /Engine/EditorLandscapeResources/SplineEditorMesh.SplineEditorMesh
[2025.08.29-00.21.38:704][  0]LogStaticMesh: Display: Construindo malha estática Cube (Estimativa de memória necessária: 0.083707 MB)...
[2025.08.29-00.21.38:707][  0]LogStaticMesh: Built static mesh [0.00s] /Engine/BasicShapes/Cube.Cube
[2025.08.29-00.21.38:715][  0]LogMaterial: Display: Missing cached shadermap for M_DefaultOddSquare in PCD3D_SM5, Default, SM5, Editor (DDC key hash: f1113690de2eab2e1fb6bae9acd618b832412515), compiling. 
[2025.08.29-00.21.38:722][  0]LogMaterial: Display: Missing cached shadermap for M_DefaultEvenSquare in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 80d98560114379bebf7d94c27e83c6e5b1159809), compiling. 
[2025.08.29-00.21.38:800][  0]LogMaterial: Display: Missing cached shadermap for HairDebugMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 8d40dbaa90fc089b670028ccd103378ae89edde6), compiling. 
[2025.08.29-00.21.38:816][  0]LogMaterial: Display: Missing cached shadermap for HairDefaultMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: b77fadeaa9a56d0391bc84d254a838308f265d8c), compiling. 
[2025.08.29-00.21.38:828][  0]LogMaterial: Display: Missing cached shadermap for HairCardsDefaultMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: f02e342ba148765adc1c71a6926a140fcb6dba39), compiling. 
[2025.08.29-00.21.38:845][  0]LogMaterial: Display: Missing cached shadermap for HairMeshesDefaultMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 71e1eb577e3cd48f1291d4c9e0b9b4d81db4c6a1), compiling. 
[2025.08.29-00.21.38:858][  0]LogMaterial: Display: Missing cached shadermap for Widget3DPassThrough in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 6053725acfccf8bc1f1ea7e736dc822bcbae65da), compiling. 
[2025.08.29-00.21.38:866][  0]LogMaterial: Display: Missing cached shadermap for Widget3DPassThrough_Translucent in PCD3D_SM5, Default, SM5, Editor (DDC key hash: a2698286c3c4fdb83edeea7381d8ce7092d8fed8), compiling. 
[2025.08.29-00.21.38:877][  0]LogMaterial: Display: Missing cached shadermap for Widget3DPassThrough_Opaque in PCD3D_SM5, Default, SM5, Editor (DDC key hash: ce41c9ee2febe3d87f89671babc46ec4d538c5d5), compiling. 
[2025.08.29-00.21.38:886][  0]LogMaterial: Display: Missing cached shadermap for Widget3DPassThrough_Opaque_OneSided in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 275869985ab6df63912785534a491a551aa8dda9), compiling. 
[2025.08.29-00.21.38:895][  0]LogMaterial: Display: Missing cached shadermap for Widget3DPassThrough_Masked in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 9d9cb80c6b1962ff45c6e0fd0c03c289905befd9), compiling. 
[2025.08.29-00.21.38:904][  0]LogMaterial: Display: Missing cached shadermap for Widget3DPassThrough_Masked_OneSided in PCD3D_SM5, Default, SM5, Editor (DDC key hash: fbbae3ac8c2ecfc1b2fa73fae42b899910e7da22), compiling. 
[2025.08.29-00.21.38:952][  0]LogMaterial: Display: Missing cached shadermap for TranslucentTransformGizmoMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 8e73e4d555c11320167aebde9d149518dd546a73), compiling. 
[2025.08.29-00.21.38:961][  0]LogMaterial: Display: Missing cached shadermap for SnapGridMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 5747164656f147a43412a3e253c5bc3e13bc5df0), compiling. 
[2025.08.29-00.21.38:969][  0]LogStaticMesh: Display: Construindo malha estática SnapGridPlaneMesh (Estimativa de memória necessária: 0.059454 MB)...
[2025.08.29-00.21.38:971][  0]LogStaticMesh: Built static mesh [0.00s] /Engine/VREditor/SnapGrid/SnapGridPlaneMesh.SnapGridPlaneMesh
[2025.08.29-00.21.38:975][  0]LogMaterial: Display: Missing cached shadermap for TransformGizmoMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 172185970281befd9a29e54bbfa6fd49f0bcf71f), compiling. 
[2025.08.29-00.21.38:980][  0]LogStaticMesh: Display: Construindo malha estática PlaneTranslationHandle (Estimativa de memória necessária: 0.189357 MB)...
[2025.08.29-00.21.38:985][  0]LogStaticMesh: Built static mesh [0.01s] /Engine/VREditor/TransformGizmo/PlaneTranslationHandle.PlaneTranslationHandle
[2025.08.29-00.21.38:988][  0]LogStaticMesh: Display: Construindo malha estática RotationHandleFull (Estimativa de memória necessária: 6.20107 MB)...
[2025.08.29-00.21.39:049][  0]LogStaticMesh: Built static mesh [0.06s] /Engine/VREditor/TransformGizmo/RotationHandleFull.RotationHandleFull
[2025.08.29-00.21.39:053][  0]LogStaticMesh: Display: Construindo malha estática RotationHandleIndicator (Estimativa de memória necessária: 0.419229 MB)...
[2025.08.29-00.21.39:060][  0]LogStaticMesh: Built static mesh [0.01s] /Engine/VREditor/TransformGizmo/RotationHandleIndicator.RotationHandleIndicator
[2025.08.29-00.21.39:064][  0]LogStaticMesh: Display: Construindo malha estática RotationHandleQuarter (Estimativa de memória necessária: 1.44741 MB)...
[2025.08.29-00.21.39:080][  0]LogStaticMesh: Built static mesh [0.02s] /Engine/VREditor/TransformGizmo/RotationHandleQuarter.RotationHandleQuarter
[2025.08.29-00.21.39:083][  0]LogStaticMesh: Display: Construindo malha estática StartRotationHandleIndicator (Estimativa de memória necessária: 0.830293 MB)...
[2025.08.29-00.21.39:093][  0]LogStaticMesh: Built static mesh [0.01s] /Engine/VREditor/TransformGizmo/StartRotationHandleIndicator.StartRotationHandleIndicator
[2025.08.29-00.21.39:097][  0]LogStaticMesh: Display: Construindo malha estática TransformGizmoFreeRotation (Estimativa de memória necessária: 43.612814 MB)...
[2025.08.29-00.21.40:681][  0]LogStaticMesh: Built static mesh [1.59s] /Engine/VREditor/TransformGizmo/TransformGizmoFreeRotation.TransformGizmoFreeRotation
[2025.08.29-00.21.40:748][  0]LogStaticMesh: Display: Construindo malha estática TranslateArrowHandle (Estimativa de memória necessária: 0.240406 MB)...
[2025.08.29-00.21.40:773][  0]LogStaticMesh: Built static mesh [0.03s] /Engine/VREditor/TransformGizmo/TranslateArrowHandle.TranslateArrowHandle
[2025.08.29-00.21.40:777][  0]LogStaticMesh: Display: Construindo malha estática UniformScaleHandle (Estimativa de memória necessária: 0.140998 MB)...
[2025.08.29-00.21.40:790][  0]LogStaticMesh: Built static mesh [0.01s] /Engine/VREditor/TransformGizmo/UniformScaleHandle.UniformScaleHandle
[2025.08.29-00.21.40:794][  0]LogStaticMesh: Display: Construindo malha estática BoundingBoxCorner (Estimativa de memória necessária: 0.240406 MB)...
[2025.08.29-00.21.40:797][  0]LogStaticMesh: Built static mesh [0.00s] /Engine/VREditor/TransformGizmo/BoundingBoxCorner.BoundingBoxCorner
[2025.08.29-00.21.40:803][  0]LogStaticMesh: Display: Construindo malha estática BoundingBoxEdge (Estimativa de memória necessária: 0.08356 MB)...
[2025.08.29-00.21.40:805][  0]LogStaticMesh: Built static mesh [0.00s] /Engine/VREditor/TransformGizmo/BoundingBoxEdge.BoundingBoxEdge
[2025.08.29-00.21.40:810][  0]LogStaticMesh: Display: Construindo malha estática S_1_Unit_Plane (Estimativa de memória necessária: 0.054254 MB)...
[2025.08.29-00.21.40:812][  0]LogStaticMesh: Built static mesh [0.00s] /Engine/ArtTools/RenderToTexture/Meshes/S_1_Unit_Plane.S_1_Unit_Plane
[2025.08.29-00.21.40:815][  0]LogMaterial: Display: Missing cached shadermap for M_SimpleUnlitTranslucent in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 11441f8abe8a8b5aad8d23a4a879133a14714190), compiling. 
[2025.08.29-00.21.40:827][  0]LogConfig: Applying CVar settings from Section [/Script/CQTest.CQTestSettings] File [Engine]
[2025.08.29-00.21.40:866][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.08.29-00.21.40:866][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.08.29-00.21.40:866][  0]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: NetActorFactory name: NetActorFactory id: 0
[2025.08.29-00.21.40:866][  0]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: NetSubObjectFactory name: NetSubObjectFactory id: 1
[2025.08.29-00.21.40:904][  0]LogSlate: Border
[2025.08.29-00.21.40:904][  0]LogSlate: BreadcrumbButton
[2025.08.29-00.21.40:904][  0]LogSlate: Brushes.Title
[2025.08.29-00.21.40:904][  0]LogSlate: ColorPicker.ColorThemes
[2025.08.29-00.21.40:904][  0]LogSlate: Default
[2025.08.29-00.21.40:904][  0]LogSlate: Icons.Save
[2025.08.29-00.21.40:904][  0]LogSlate: Icons.Toolbar.Settings
[2025.08.29-00.21.40:904][  0]LogSlate: ListView
[2025.08.29-00.21.40:904][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.08.29-00.21.40:904][  0]LogSlate: SoftwareCursor_Grab
[2025.08.29-00.21.40:904][  0]LogSlate: TableView.DarkRow
[2025.08.29-00.21.40:904][  0]LogSlate: TableView.Row
[2025.08.29-00.21.40:904][  0]LogSlate: TreeView
[2025.08.29-00.21.40:986][  0]LogMaterial: Display: Missing cached shadermap for BaseColor in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 7c3f00ab01590c2fb7f73eb5ce13f6541d433598), compiling. 
[2025.08.29-00.21.40:992][  0]LogMaterial: Display: Missing cached shadermap for CustomDepth in PCD3D_SM5, Default, SM5, Editor (DDC key hash: f277bae5ea96650af1eb7bfdc5213df839711efb), compiling. 
[2025.08.29-00.21.41:009][  0]LogMaterial: Display: Missing cached shadermap for CustomStencil in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 89d76ecc6cc177f3b2d3222bd7660a11dd36243b), compiling. 
[2025.08.29-00.21.41:017][  0]LogMaterial: Display: Missing cached shadermap for FinalImage in PCD3D_SM5, Default, SM5, Editor (DDC key hash: d5411c94d2c258ea0499aff779f7d64583a707ad), compiling. 
[2025.08.29-00.21.41:021][  0]LogMaterial: Display: Missing cached shadermap for LightingModel in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 51dec87c68eda1760e4e95ab31ff170d07fc96db), compiling. 
[2025.08.29-00.21.41:030][  0]LogMaterial: Display: Missing cached shadermap for MaterialAO in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 0a86f6a25838a7566dddedc9b8c3c36c757097cc), compiling. 
[2025.08.29-00.21.41:039][  0]LogMaterial: Display: Missing cached shadermap for Metallic in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 5a80e17898126aedadb1fc604ddd5fd637571119), compiling. 
[2025.08.29-00.21.41:047][  0]LogMaterial: Display: Missing cached shadermap for Opacity in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 1395694637c43d3bac1ba8b22747f7fde0b6c35c), compiling. 
[2025.08.29-00.21.41:056][  0]LogMaterial: Display: Missing cached shadermap for Roughness in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 24134510866ca74e2f31d11c98ea6f32045fe94d), compiling. 
[2025.08.29-00.21.41:066][  0]LogMaterial: Display: Missing cached shadermap for Anisotropy in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 3cf44560c192dbaef27e7698758a26f1883c1db1), compiling. 
[2025.08.29-00.21.41:074][  0]LogMaterial: Display: Missing cached shadermap for SceneColor in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 6efe10a9c9aad272809e0788e248fa36e19f2194), compiling. 
[2025.08.29-00.21.41:082][  0]LogMaterial: Display: Missing cached shadermap for SceneDepth in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 164c2563ebb51ceb3f12aba72cacb316908db4e7), compiling. 
[2025.08.29-00.21.41:094][  0]LogMaterial: Display: Missing cached shadermap for SeparateTranslucencyRGB in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 7213ba6b5ea0a6283e269cc9d5c10bbfaad911ff), compiling. 
[2025.08.29-00.21.41:104][  0]LogMaterial: Display: Missing cached shadermap for SeparateTranslucencyA in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 2ba3da3f72f5004a1468157c62ad304c80d26dc4), compiling. 
[2025.08.29-00.21.41:112][  0]LogMaterial: Display: Missing cached shadermap for Specular in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 26eb602b2bd8cd057be8c17d67e61ec8ecd7aa30), compiling. 
[2025.08.29-00.21.41:121][  0]LogMaterial: Display: Missing cached shadermap for SubsurfaceColor in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 830ce0cc75040a72ded5a12692a8d99452f0bcc3), compiling. 
[2025.08.29-00.21.41:129][  0]LogMaterial: Display: Missing cached shadermap for WorldNormal in PCD3D_SM5, Default, SM5, Editor (DDC key hash: ad6380af3aee21d02859e3d6d975a219dc71a7a6), compiling. 
[2025.08.29-00.21.41:137][  0]LogMaterial: Display: Missing cached shadermap for WorldTangent in PCD3D_SM5, Default, SM5, Editor (DDC key hash: aa79264f4cbffaaffccf8de433ee544c9765c4c3), compiling. 
[2025.08.29-00.21.41:148][  0]LogMaterial: Display: Missing cached shadermap for AmbientOcclusion in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 3334d836a5a320a3d55cda76626c03903915b400), compiling. 
[2025.08.29-00.21.41:158][  0]LogMaterial: Display: Missing cached shadermap for CustomDepthWorldUnits in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 71df046b75f498a6af204e1db127985dd74a361c), compiling. 
[2025.08.29-00.21.41:167][  0]LogMaterial: Display: Missing cached shadermap for SceneDepthWorldUnits in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 94340d3251dbf59e3764f339ac41a07188424610), compiling. 
[2025.08.29-00.21.41:174][  0]LogMaterial: Display: Missing cached shadermap for Velocity in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 7126ff7280b83c1e217eef49aff51db00b66f2d5), compiling. 
[2025.08.29-00.21.41:182][  0]LogMaterial: Display: Missing cached shadermap for PreTonemapHDRColor in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 3f785c83d2991f36bf2e1f8c90ab1774cb8d42aa), compiling. 
[2025.08.29-00.21.41:190][  0]LogMaterial: Display: Missing cached shadermap for PostTonemapHDRColor in PCD3D_SM5, Default, SM5, Editor (DDC key hash: f93e813cfe83bebf011e65ade39f5afdf1d33b23), compiling. 
[2025.08.29-00.21.41:270][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.08.29-00.21.41:276][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 5.256 ms
[2025.08.29-00.21.41:317][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.08.29-00.21.41:317][  0]LogInit: XR: MultiViewport is Disabled
[2025.08.29-00.21.41:317][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.08.29-00.21.41:394][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.08.29-00.21.41:418][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: 1C61C20866754E098000000000001E00 | Instance: 2125A2464B365ED6CAE23EB43F127CD7 (TKT-31384).
[2025.08.29-00.21.41:825][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.08.29-00.21.41:825][  0]LogNNERuntimeORT: Selecting NPU adapter: Intel(R) Iris(R) Xe Graphics
[2025.08.29-00.21.41:974][  0]LogNNERuntimeORT: MakeRuntimeORTDml:
[2025.08.29-00.21.41:975][  0]LogNNERuntimeORT:   DirectML:  yes
[2025.08.29-00.21.41:975][  0]LogNNERuntimeORT:   RHI D3D12: no
[2025.08.29-00.21.41:975][  0]LogNNERuntimeORT:   D3D12:     yes
[2025.08.29-00.21.41:975][  0]LogNNERuntimeORT:   NPU:       yes
[2025.08.29-00.21.41:975][  0]LogNNERuntimeORT: Interface availability:
[2025.08.29-00.21.41:975][  0]LogNNERuntimeORT:   GPU: yes
[2025.08.29-00.21.41:975][  0]LogNNERuntimeORT:   RDG: no
[2025.08.29-00.21.41:975][  0]LogNNERuntimeORT:   NPU: yes
[2025.08.29-00.21.42:108][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.08.29-00.21.42:108][  0]LogNNERuntimeORT: Selecting NPU adapter: Intel(R) Iris(R) Xe Graphics
[2025.08.29-00.21.42:267][  0]LogMaterial: Display: Missing cached shadermap for DefaultSpriteMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 2b31f0507d9ae817bf36b0f8b78156f9cae5699d), compiling. 
[2025.08.29-00.21.42:290][  0]LogMaterial: Display: Missing cached shadermap for OpaqueUnlitSpriteMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: ff151a63f71ce7e06ba30a3da57bf778f0b2937f), compiling. 
[2025.08.29-00.21.42:359][  0]LogMLAdapter: Warning: Neural network asset data not set
[2025.08.29-00.21.42:539][  0]LogMaterial: Display: Missing cached shadermap for ControlRigXRayMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: afafc03ac1721c38332d27f91db1a8ad724eefed), compiling. 
[2025.08.29-00.21.42:565][  0]LogMaterial: Display: Missing cached shadermap for M_Manip in PCD3D_SM5, Default, SM5, Editor (DDC key hash: e2e603b13b35187b599a5dc995491a1f437e4395), compiling. 
[2025.08.29-00.21.42:954][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.08.29-00.21.42:955][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.08.29-00.21.42:964][  0]LogMetaSound: MetaSound Engine Initialized
[2025.08.29-00.21.43:071][  0]LogChaosVDEditor: [FChaosVDExtensionsManager::RegisterExtension] Registering CVD Extension [FChaosVDGenericDebugDrawExtension] ...
[2025.08.29-00.21.43:071][  0]LogChaosVDEditor: [FChaosVDExtensionsManager::RegisterExtension] Registering CVD Extension [FChaosVDAccelerationStructuresExtension] ...
[2025.08.29-00.21.43:083][  0]LogTimingProfiler: Initialize
[2025.08.29-00.21.43:083][  0]LogTimingProfiler: OnSessionChanged
[2025.08.29-00.21.43:083][  0]LoadingProfiler: Initialize
[2025.08.29-00.21.43:083][  0]LoadingProfiler: OnSessionChanged
[2025.08.29-00.21.43:084][  0]LogNetworkingProfiler: Initialize
[2025.08.29-00.21.43:084][  0]LogNetworkingProfiler: OnSessionChanged
[2025.08.29-00.21.43:084][  0]LogMemoryProfiler: Initialize
[2025.08.29-00.21.43:084][  0]LogMemoryProfiler: OnSessionChanged
[2025.08.29-00.21.43:190][  0]LogConfig: Applying CVar settings from Section [/Script/PCG.PCGEngineSettings] File [Engine]
[2025.08.29-00.21.43:301][  0]LogStaticMesh: Display: Construindo malha estática SM_MediaPlateScreen (Estimativa de memória necessária: 0.010915 MB)...
[2025.08.29-00.21.43:312][  0]LogStaticMesh: Built static mesh [0.01s] /MediaPlate/SM_MediaPlateScreen.SM_MediaPlateScreen
[2025.08.29-00.21.43:340][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.08.29-00.21.43:462][  0]SourceControl: Controle de revisão desabilitado
[2025.08.29-00.21.43:476][  0]SourceControl: Controle de revisão desabilitado
[2025.08.29-00.21.43:576][  0]LogMaterial: Display: Missing cached shadermap for SpeedTreeMaster in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 94ebb381d9a275fe7fc165c96064e84c47b3046a), compiling. 
[2025.08.29-00.21.43:592][  0]LogMaterial: Display: Missing cached shadermap for SpeedTreeBillboardMaster in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 357107a492ba49fba109e18a1f5911d9f5f413d1), compiling. 
[2025.08.29-00.21.43:861][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.08.29-00.21.43:879][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.08.29-00.21.43:879][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.08.29-00.21.43:996][  0]LogTemp: Display: Unreal MCP Module has started
[2025.08.29-00.21.44:029][  0]LogMaterial: Display: Missing cached shadermap for BasicShapeMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: b8723fb3a364c66ef789b413393bec027dd076e8), compiling. 
[2025.08.29-00.21.44:037][  0]LogMaterial: Display: Missing cached shadermap for DataflowVertexMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 88d9ebaa4a3bca1e6dbdaa3db9c334e24b15d193), compiling. 
[2025.08.29-00.21.44:048][  0]LogMaterial: Display: Missing cached shadermap for DataflowTwoSidedVertexMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 4d0d0581db6d08cf938e09b309ad3b0472e3bb90), compiling. 
[2025.08.29-00.21.44:054][  0]LogMaterial: Display: Missing cached shadermap for SculptMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: f1296849cb8d7196f1bd9bdc5f518d843af8e162), compiling. 
[2025.08.29-00.21.44:102][  0]LogUObjectArray: 45094 objects as part of root set at end of initial load.
[2025.08.29-00.21.44:102][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.08.29-00.21.44:209][  0]LogInit: Executing Class /Script/UnrealEd.WorldPartitionBuilderCommandlet
[2025.08.29-00.21.44:209][  0]LogInit: Initializing Editor Engine...
[2025.08.29-00.21.44:209][  0]LogEngine: Initializing Engine...
[2025.08.29-00.21.44:218][  0]LogMaterial: Display: Missing cached shadermap for LayerVisMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 704b8e0d7b425ec7d6048acd393479e08cf70e2e), compiling. 
[2025.08.29-00.21.44:224][  0]LogMaterial: Display: Missing cached shadermap for LandscapeMaterialInstanceConstant_LayerVisMaterial_0 in PCD3D_SM5, Default, SM5, Editor (DDC key hash: d1bad0974e382556c0b564fb98daf78367b2b0d6), compiling. 
[2025.08.29-00.21.44:238][  0]LogMaterial: Display: Missing cached shadermap for SelectBrushMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 6e8c53dcfad4c330ee2d2953588c86dd91ba924a), compiling. 
[2025.08.29-00.21.44:246][  0]LogMaterial: Display: Missing cached shadermap for LandscapeMaterialInstanceConstant_SelectBrushMaterial_Selected_0 in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 9bea796c04fed20b87df833e562567c3d6762cf9), compiling. 
[2025.08.29-00.21.44:256][  0]LogMaterial: Display: Missing cached shadermap for SelectBrushMaterial_SelectedRegion in PCD3D_SM5, Default, SM5, Editor (DDC key hash: d6d9f862479e3921d2fd7d549be783d7b33be686), compiling. 
[2025.08.29-00.21.44:263][  0]LogMaterial: Display: Missing cached shadermap for LandscapeMaterialInstanceConstant_SelectBrushMaterial_SelectedRegion_0 in PCD3D_SM5, Default, SM5, Editor (DDC key hash: bfecde32f4d24396ac9bbd979f782e2676df7f29), compiling. 
[2025.08.29-00.21.44:275][  0]LogMaterial: Display: Missing cached shadermap for MaskBrushMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 0a907a5f7ca2c9be678aabb2f3d27204771b44ac), compiling. 
[2025.08.29-00.21.44:285][  0]LogMaterial: Display: Missing cached shadermap for LandscapeMaterialInstanceConstant_MaskBrushMaterial_MaskedRegion_0 in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 778211b3796ed52b4993a7fc548bf8c76a4c0f17), compiling. 
[2025.08.29-00.21.44:295][  0]LogMaterial: Display: Missing cached shadermap for ColorMaskBrushMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 6b602d950f6d43278deb33b59cf97f67520a82c5), compiling. 
[2025.08.29-00.21.44:302][  0]LogMaterial: Display: Missing cached shadermap for LandscapeMaterialInstanceConstant_ColorMaskBrushMaterial_MaskedRegion_0 in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 6b9f7769b53d71b35bfdbed6de7e7b2375a326c9), compiling. 
[2025.08.29-00.21.44:311][  0]LogMaterial: Display: Missing cached shadermap for LandscapeDirtyMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 75f8fb2ddba43e76c519f5233a6bad79c3c3480c), compiling. 
[2025.08.29-00.21.44:321][  0]LogMaterial: Display: Missing cached shadermap for LandscapeMaterialInstanceConstant_LandscapeDirtyMaterial_0 in PCD3D_SM5, Default, SM5, Editor (DDC key hash: ab9f2906834c3cf80d602332bc58a1d9db989b81), compiling. 
[2025.08.29-00.21.44:354][  0]LogMaterial: Display: Missing cached shadermap for LandscapeLayerUsageMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: d173b41bf083c083d136d35a46336d079c2fa12a), compiling. 
[2025.08.29-00.21.44:363][  0]LogMaterial: Display: Missing cached shadermap for LandscapeMaterialInstanceConstant_LandscapeLayerUsageMaterial_0 in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 722c538bb2cea98cf2ee4a4fb5e602b49383efe8), compiling. 
[2025.08.29-00.21.44:386][  0]LogTedsSettings: UTedsSettingsEditorSubsystem::Initialize
[2025.08.29-00.21.44:386][  0]LogTemp: Display: UnrealMCPBridge: Initializing
[2025.08.29-00.21.44:387][  0]LogTemp: Display: UnrealMCPBridge: Server started on 127.0.0.1:55557
[2025.08.29-00.21.44:387][  0]LogTemp: Display: MCPServerRunnable: Created server runnable
[2025.08.29-00.21.44:391][  0]LogTemp: Display: MCPServerRunnable: Server thread starting...
[2025.08.29-00.21.44:404][  0]LogMaterial: Display: Missing cached shadermap for WireframeMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: c04c159b6e98249b1bef744f20368f12c5f51b82), compiling. Is special engine material.
[2025.08.29-00.21.44:500][  0]LogMaterial: Display: Missing cached shadermap for LevelColorationLitMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: ce83048db4cb3af3c394cd227a64eb66c771752d), compiling. Is special engine material.
[2025.08.29-00.21.44:577][  0]LogMaterial: Display: Missing cached shadermap for LevelColorationUnlitMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 863ca7032a79097a8b7e586dd33529746292ff35), compiling. Is special engine material.
[2025.08.29-00.21.44:644][  0]LogMaterial: Display: Missing cached shadermap for MAT_LevelColorationLitLightmapUV in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 5ac143727df8e2ca6690670d504faf740227eed3), compiling. Is special engine material.
[2025.08.29-00.21.44:746][  0]LogMaterial: Display: Missing cached shadermap for ShadedLevelColorationLitMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 7bf448340987bb864df8b0a6f620c90b5904c2a4), compiling. Is special engine material.
[2025.08.29-00.21.44:837][  0]LogMaterial: Display: Missing cached shadermap for ShadedLevelColorationUnlitMateri in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 7512d7e1699b4bdd51e5bbb672de2bc2c8aeb5bd), compiling. Is special engine material.
[2025.08.29-00.21.44:912][  0]LogMaterial: Display: Missing cached shadermap for VertexColorMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 8f73847f1c4662093a3e24e56a56538fa3b86506), compiling. 
[2025.08.29-00.21.44:934][  0]LogMaterial: Display: Missing cached shadermap for VertexColorViewMode_ColorOnly in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 9bb4751e5de0ed24fe97be6d8cdc4c4c0b93f6ca), compiling. 
[2025.08.29-00.21.44:945][  0]LogMaterial: Display: Missing cached shadermap for VertexColorViewMode_AlphaAsColor in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 464ec3ea86eed7c02ddb265e6791badbf458d206), compiling. 
[2025.08.29-00.21.44:956][  0]LogMaterial: Display: Missing cached shadermap for VertexColorViewMode_RedOnly in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 152dc9b67d26a1d12184aaaf41985b3efec6b8ca), compiling. 
[2025.08.29-00.21.44:969][  0]LogMaterial: Display: Missing cached shadermap for VertexColorViewMode_GreenOnly in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 75fbe820847d9c619e14e5ab20d5bbe8d3e06434), compiling. 
[2025.08.29-00.21.44:980][  0]LogMaterial: Display: Missing cached shadermap for VertexColorViewMode_BlueOnly in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 05326c96a94f677aee6fd59d914bc50988cd54a3), compiling. 
[2025.08.29-00.21.44:994][  0]LogMaterial: Display: Missing cached shadermap for TextureColorViewMode in PCD3D_SM5, Default, SM5, Editor (DDC key hash: b62eaef35cfed8f23f294f5e201f73a8b5f510d5), compiling. 
[2025.08.29-00.21.45:007][  0]LogMaterial: Display: Missing cached shadermap for NaniteHiddenSectionMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 05a12b9b6d979dd8b8fc57e92ea655f2bcae4ae1), compiling. 
[2025.08.29-00.21.45:020][  0]LogMaterial: Display: Missing cached shadermap for RemoveSurfaceMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 9f9114ed134a4a5e16d74bb7c24ce1fdf8b14659), compiling. 
[2025.08.29-00.21.45:026][  0]LogMaterial: Display: Missing cached shadermap for DebugMeshMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 2d73805bfaf61c4bd8b516e8c750492dc29bb033), compiling. 
[2025.08.29-00.21.45:040][  0]LogMaterial: Display: Missing cached shadermap for EmissiveMeshMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 877dac3c19561b00f2a43c6918f2c9349b12f800), compiling. 
[2025.08.29-00.21.45:054][  0]LogMaterial: Display: Missing cached shadermap for M_InvalidLightmapSettings in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 131c0df97f59291b1488f187e79db4bdcdcb7efc), compiling. 
[2025.08.29-00.21.45:063][  0]LogMaterial: Display: Missing cached shadermap for GizmoMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 490fd34955c69990132406285b0356dd0ac6ef20), compiling. 
[2025.08.29-00.21.45:076][  0]LogMaterial: Display: Missing cached shadermap for PhAT_JointLimitMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 62c67de9fa8f114ac4315143b59bd7fd912b9e12), compiling. 
[2025.08.29-00.21.45:098][  0]LogMaterial: Display: Missing cached shadermap for FlattenMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 760f2d26615404b6a9649924a96573edee489089), compiling. 
[2025.08.29-00.21.45:115][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.08.29-00.21.45:122][  0]LogMaterial: Display: Missing cached shadermap for FlattenMaterial_VT in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 5b3d85cd7788d7f7aa1dcda54f85ce7bb98c728d), compiling. 
[2025.08.29-00.21.45:137][  0]LogMaterial: Display: Missing cached shadermap for FlattenMaterial_WS_Normal_VT in PCD3D_SM5, Default, SM5, Editor (DDC key hash: bdd4a250eb5ac339679fc4d09e935e04986e8bd8), compiling. 
[2025.08.29-00.21.45:154][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.08.29-00.21.45:175][  0]LogChaosDD: Not creating Chaos Debug Draw Scene for world Untitled
[2025.08.29-00.21.45:199][  0]LogNetVersion: Set ProjectVersion to 1.0.0.0. Version Checksum will be recalculated on next use.
[2025.08.29-00.21.45:199][  0]LogInit: Texture streaming: Enabled
[2025.08.29-00.21.45:213][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.08.29-00.21.45:221][  0]LogInit: Initializing Editor Engine Completed
[2025.08.29-00.21.45:255][  0]LogMaterial: Display: Missing cached shadermap for M_DynamicMeshComponentVtxColor in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 9a6ed5c4cf5dbd83e7252ea7e6cf956c6a666f5d), compiling. 
[2025.08.29-00.21.45:300][  0]LogMaterial: Display: Missing cached shadermap for DefaultMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 78acee75a5c4c94cf9c8a4f5a6d97fb34c71848e), compiling. 
[2025.08.29-00.21.45:318][  0]LogNNEDenoiser: Ray Tracing is not enabled, therefore NNEDenoiser is not registered!
[2025.08.29-00.21.45:320][  0]LogPython: Python enabled via CVar 'Engine.Python.IsEnabledByDefault'
[2025.08.29-00.21.45:320][  0]LogPython: Using Python 3.11.8
[2025.08.29-00.21.45:391][  0]LogPython: Display: No pip-enabled plugins with python dependencies found, skipping
[2025.08.29-00.21.46:933][  0]LogMLAdapter: Creating MLAdapter manager of class MLAdapterManager
[2025.08.29-00.21.47:150][  0]LogComputeFramework: DG_LinearBlendSkin_Morph_Cloth/UpdateGraph_ComputeGraph/LinearBlendSkin_Morph_Cloth_OptimusNode_ComputeKernel_1: Loaded PCD3D_SM5 shaders from DDC.
[2025.08.29-00.21.47:209][  0]LogComputeFramework: DG_LinearBlendSkin_Morph_Cloth_RecomputeNormals/UpdateGraph_ComputeGraph/LinearBlendSkin_Morph_Cloth_PositionOnly_OptimusNode_ComputeKernel_1: Loaded PCD3D_SM5 shaders from DDC.
[2025.08.29-00.21.47:248][  0]LogComputeFramework: DG_LinearBlendSkin_Morph_Cloth_RecomputeNormals/UpdateGraph_ComputeGraph/ComputeNormalsTangents_OptimusNode_CustomComputeKernel_0: Loaded PCD3D_SM5 shaders from DDC.
[2025.08.29-00.21.47:278][  0]LogMaterial: Display: Missing cached shadermap for PhAT_UnselectedMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: 28288cfee262a6a8ca17d32e1d02530c825c492f), compiling. 
[2025.08.29-00.21.47:287][  0]LogMaterial: Display: Missing cached shadermap for PhAT_NoCollisionMaterial in PCD3D_SM5, Default, SM5, Editor (DDC key hash: c6a8529651c270a31721e16f849ebca3b7feadc9), compiling. 
[2025.08.29-00.21.47:467][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.08.29-00.21.47:525][  0]LogEditorDataStorage: Initializing
[2025.08.29-00.21.47:532][  0]LogEditorDataStorage: Initialized
[2025.08.29-00.21.47:535][  0]SourceControl: Controle de revisão desabilitado
[2025.08.29-00.21.47:535][  0]LogWorldPartitionBuilderCommandlet: Display: Execution started...
[2025.08.29-00.21.47:543][  0]LogCollectionManager: Loaded 0 collections in 0.003677 seconds
[2025.08.29-00.21.47:569][  0]LogFileCache: Scanning file cache for directory 'C:/Game/AURACRON/Saved/Collections/' took 0.03s
[2025.08.29-00.21.47:593][  0]LogFileCache: Scanning file cache for directory 'C:/Game/AURACRON/Content/Developers/tktca/Collections/' took 0.02s
[2025.08.29-00.21.47:604][  0]LogFileCache: Scanning file cache for directory 'C:/Game/AURACRON/Content/Collections/' took 0.01s
[2025.08.29-00.21.47:606][  0]LogPackageName: SearchForPackageOnDisk took   0.000s to resolve /Game/NewMap.
[2025.08.29-00.21.47:607][  0]LogPackageName: SearchForPackageOnDisk took   0.000s to resolve /Game/NewMap.
[2025.08.29-00.21.47:607][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.08.29-00.21.47:828][  0]LogAssetRegistry: AssetRegistryGather time 0.1811s: AssetDataDiscovery 0.0214s, AssetDataGather 0.0218s, StoreResults 0.1379s. Wall time 330.1720s.
	NumCachedDirectories 1647. NumUncachedDirectories 21. NumCachedFiles 7994. NumUncachedFiles 2.
	BackgroundTickInterruptions 0.
[2025.08.29-00.21.47:861][  0]LogPlacementMode: Display: The Asset Registry is done with its initial scan, the list of placeable classes has been updated.
[2025.08.29-00.21.47:861][  0]LogCollectionManager: Fixed up redirectors for 0 collections in 0.000001 seconds (updated 0 objects)
[2025.08.29-00.21.47:861][  0]SourceControl: Controle de revisão desabilitado
[2025.08.29-00.21.47:862][  0]LogChaosDD: Not creating Chaos Debug Draw Scene for world NewMap
[2025.08.29-00.21.47:867][  0]LogPCGWorldPartitionBuilder: Display: Initialize Builder Args
[2025.08.29-00.21.47:868][  0]LogPCGWorldPartitionBuilder: Display: Loaded PCGBuilderSettings '/Game/NewPCGBuilderSettings.NewPCGBuilderSettings'
[2025.08.29-00.21.47:869][  0]LogPCGWorldPartitionBuilder: Display: bGenerateEditingModeLoadAsPreviewComponents : '1' (PCGBuilderSettings)
[2025.08.29-00.21.47:869][  0]LogPCGWorldPartitionBuilder: Display: bGenerateEditingModeNormalComponents : '0' (PCGBuilderSettings)
[2025.08.29-00.21.47:869][  0]LogPCGWorldPartitionBuilder: Display: bGenerateEditingModePreviewComponents : '0' (PCGBuilderSettings)
[2025.08.29-00.21.47:870][  0]LogPCGWorldPartitionBuilder: Display: bOneComponentAtATime : '0' (PCGBuilderSettings)
[2025.08.29-00.21.47:870][  0]LogPCGWorldPartitionBuilder: Display: bIgnoreGenerationErrors : '0' (PCGBuilderSettings)
[2025.08.29-00.21.47:870][  0]LogPCGWorldPartitionBuilder: Display: IncludeActorIDs : 'None' (PCGBuilderSettings)
[2025.08.29-00.21.47:870][  0]LogPCGWorldPartitionBuilder: Display: IncludeGraphNames : 'None' (Default)
[2025.08.29-00.21.47:871][  0]LogPCGWorldPartitionBuilder: Display: IncludeGraphs 'None' (PCGBuilderSettings)
[2025.08.29-00.21.47:871][  0]LogPCGWorldPartitionBuilder: Display: bIterativeCellLoading : '0' (PCGBuilderSettings)
[2025.08.29-00.21.47:871][  0]LogPCGWorldPartitionBuilder: Display: IterativeCellSize : '25600' (PCGBuilderSettings)
[2025.08.29-00.21.47:871][  0]LogPCGWorldPartitionBuilder: Display: IncludedDataLayers 'None' (PCGBuilderSettings)
[2025.08.29-00.21.47:871][  0]LogPCGWorldPartitionBuilder: Display: ExcludedDataLayers 'None' (PCGBuilderSettings)
[2025.08.29-00.21.47:876][  0]LogShaderCompilers: Display: ================================================
[2025.08.29-00.21.47:877][  0]LogShaderCompilers: Display: === FShaderJobCache stats ===
[2025.08.29-00.21.47:877][  0]LogShaderCompilers: Display: Total job queries 2.349, among them cache hits 57 (2.43%), DDC hits 0 (0.00%), Duplicates 426 (18.14%)
[2025.08.29-00.21.47:878][  0]LogShaderCompilers: Display: Tracking 1.866 distinct input hashes that result in 459 distinct outputs (24.60%)
[2025.08.29-00.21.47:878][  0]LogShaderCompilers: Display: RAM used: 1,42 MiB of 1,60 GiB budget. Usage: 0.09%
[2025.08.29-00.21.47:879][  0]LogShaderCompilers: Display: === Shader Compilation stats ===
[2025.08.29-00.21.47:879][  0]LogShaderCompilers: Display: Shaders Compiled: 685
[2025.08.29-00.21.47:879][  0]LogShaderCompilers: Display: Jobs assigned 775, completed 685 (88.39%)
[2025.08.29-00.21.47:879][  0]LogShaderCompilers: Display: Average time worker was idle: 0.78 s
[2025.08.29-00.21.47:879][  0]LogShaderCompilers: Display: Time job spent in pending queue: average 71.54 s, longest 249.01 s
[2025.08.29-00.21.47:880][  0]LogShaderCompilers: Display: Job execution time: average 36.76 s, max 44.83 s
[2025.08.29-00.21.47:880][  0]LogShaderCompilers: Display: Job life time (pending + execution): average 117.55 s, max 250.04
[2025.08.29-00.21.47:880][  0]LogShaderCompilers: Display: Shader code size: total 3,86 MiB, numShaders 708, average 5,583 KiB, min 200 B, max 31,645 KiB
[2025.08.29-00.21.47:880][  0]LogShaderCompilers: Display: Time at least one job was in flight (either pending or executed): 327.05 s
[2025.08.29-00.21.47:880][  0]LogShaderCompilers: Display: Mutex wait stall in FShaderJobCache::SubmitJobs:  0.00%
[2025.08.29-00.21.47:881][  0]LogShaderCompilers: Display: Jobs were issued in 14 batches (only local compilation was used), average 2.00 jobs/batch
[2025.08.29-00.21.47:881][  0]LogShaderCompilers: Display: Average processing rate: 2.09 jobs/sec
[2025.08.29-00.21.47:881][  0]LogShaderCompilers: Display: Total thread time: 325,748 s
[2025.08.29-00.21.47:881][  0]LogShaderCompilers: Display: Total thread preprocess time: 22,393 s
[2025.08.29-00.21.47:881][  0]LogShaderCompilers: Display: Percentage time preprocessing: 6.87%
[2025.08.29-00.21.47:908][  0]LogShaderCompilers: Display: Effective parallelization: 1.00 (times faster than compiling all shaders on one thread). Compare with number of workers: 9 - 0.110669
[2025.08.29-00.21.47:909][  1]LogShaderCompilers: Display: Top 5 most expensive shader types by average time:
[2025.08.29-00.21.47:909][  1]LogShaderCompilers: Display:                                             FDebugViewModePS (compiled   22 times, average 1.87 sec, max 2.72 sec, min 0.98 sec)
[2025.08.29-00.21.47:909][  1]LogShaderCompilers: Display:                                FNiagaraVertexFactoryExportCS (compiled    2 times, average 1.29 sec, max 1.57 sec, min 1.01 sec)
[2025.08.29-00.21.47:910][  1]LogShaderCompilers: Display:                        FLumenTranslucencyRadianceCacheMarkPS (compiled    3 times, average 0.77 sec, max 1.11 sec, min 0.53 sec)
[2025.08.29-00.21.47:910][  1]LogShaderCompilers: Display:                                                  FVelocityPS (compiled   23 times, average 0.70 sec, max 1.07 sec, min 0.42 sec)
[2025.08.29-00.21.47:911][  1]LogShaderCompilers: Display:                   TBasePassPSFSelfShadowedTranslucencyPolicy (compiled    3 times, average 0.69 sec, max 0.82 sec, min 0.62 sec)
[2025.08.29-00.21.47:911][  1]LogShaderCompilers: Display: Top 5 shader types by total compile time:
[2025.08.29-00.21.47:912][  1]LogShaderCompilers: Display:                                             FDebugViewModePS - 12.61% of total time (compiled   22 times, average 1.87 sec, max 2.72 sec, min 0.98 sec)
[2025.08.29-00.21.47:912][  1]LogShaderCompilers: Display:                                                  FVelocityPS - 4.96% of total time (compiled   23 times, average 0.70 sec, max 1.07 sec, min 0.42 sec)
[2025.08.29-00.21.47:913][  1]LogShaderCompilers: Display:                                 TBasePassPSFNoLightMapPolicy - 4.59% of total time (compiled   28 times, average 0.53 sec, max 0.88 sec, min 0.30 sec)
[2025.08.29-00.21.47:914][  1]LogShaderCompilers: Display:                         TBasePassPSFNoLightMapPolicySkylight - 4.42% of total time (compiled   25 times, average 0.58 sec, max 1.41 sec, min 0.31 sec)
[2025.08.29-00.21.47:914][  1]LogShaderCompilers: Display:                                             FDebugViewModeVS - 3.87% of total time (compiled   29 times, average 0.43 sec, max 1.29 sec, min 0.21 sec)
[2025.08.29-00.21.47:915][  1]LogShaderCompilers: Display: === Material stats ===
[2025.08.29-00.21.47:915][  1]LogShaderCompilers: Display: Materials Cooked:        0
[2025.08.29-00.21.47:915][  1]LogShaderCompilers: Display: Materials Translated:    101
[2025.08.29-00.21.47:915][  1]LogShaderCompilers: Display: Material Total Translate Time: 0.14 s
[2025.08.29-00.21.47:916][  1]LogShaderCompilers: Display: Material Translation Only: 0.06 s (45%)
[2025.08.29-00.21.47:916][  1]LogShaderCompilers: Display: Material DDC Serialization Only: 0.00 s (0%)
[2025.08.29-00.21.47:916][  1]LogShaderCompilers: Display: Material Cache Hits: 0 (0%)
[2025.08.29-00.21.47:916][  1]LogShaderCompilers: Display: ================================================
[2025.08.29-00.21.47:917][  1]LocalizationService: O serviço de localização está desativado.
[2025.08.29-00.21.47:917][  1]LogContentStreaming: Texture pool size now 1000 MB
[2025.08.29-00.21.47:917][  1]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.08.29-00.21.47:917][  1]LogPython: Display: Executando código de inicialização C:/Program Files/Epic Games/UE_5.6/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.08.29-00.21.48:025][  1]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.08.29-00.21.48:035][  1]LogPython: Display: Executando código de inicialização C:/Program Files/Epic Games/UE_5.6/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 124.719 ms
[2025.08.29-00.21.49:876][  2]LogStaticMesh: Display: Construindo malha estática Sphere (Estimativa de memória necessária: 1.522765 MB)...
[2025.08.29-00.21.49:879][  2]LogStaticMesh: Display: Construindo malha estática Cylinder (Estimativa de memória necessária: 0.815833 MB)...
[2025.08.29-00.21.49:881][  2]LogStaticMesh: Display: Construindo malha estática Plane (Estimativa de memória necessária: 0.010915 MB)...
[2025.08.29-00.21.49:884][  2]LogStaticMesh: Display: Construindo malha estática Cone (Estimativa de memória necessária: 0.410497 MB)...
[2025.08.29-00.21.49:889][  2]LogStaticMesh: Built static mesh [0.02s] /Engine/BasicShapes/Cylinder.Cylinder
[2025.08.29-00.21.49:892][  2]LogStaticMesh: Built static mesh [0.01s] /Engine/BasicShapes/Plane.Plane
[2025.08.29-00.21.49:897][  2]LogStaticMesh: Built static mesh [0.01s] /Engine/BasicShapes/Cone.Cone
[2025.08.29-00.21.49:900][  2]LogStaticMesh: Built static mesh [0.03s] /Engine/BasicShapes/Sphere.Sphere
[2025.08.29-00.21.50:243][  2]LogAssetRegistry: Display: Asset registry cache written as 74.3 MiB to ../../../../../../Game/AURACRON/Intermediate/CachedAssetRegistry_*.bin
