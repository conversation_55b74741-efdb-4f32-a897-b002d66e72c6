#pragma once

#include "CoreMinimal.h"
#include "Json.h"
#include "Engine/DataTable.h"
#include "UObject/ObjectMacros.h"

// Forward declaration - struct defined in .cpp to avoid GENERATED_BODY issues
struct FAuracronCollisionProfileRow;

// Core Collision APIs - UE 5.6.1 Modern
#include "Engine/CollisionProfile.h"
#include "PhysicsEngine/PhysicsSettings.h"
#include "PhysicsEngine/BodyInstance.h"
#include "PhysicsEngine/BodySetup.h"
#include "Components/PrimitiveComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SkeletalMeshComponent.h"

// Chaos Physics APIs - UE 5.6.1 Experimental
#include "ChaosModule.h"
#include "ChaosSolversModule.h"
#include "PBDRigidsSolver.h"
#include "PhysicsEngine/PhysicsObjectExternalInterface.h"
#include "PhysicsEngine/PhysicsObjectBlueprintLibrary.h"

// Advanced Collision APIs - UE 5.6.1 Enhanced
#include "PhysicsEngine/PhysicsConstraintComponent.h"
#include "PhysicsEngine/PhysicsCollisionHandler.h"
#include "PhysicsEngine/ConstraintInstance.h"
#include "PhysicsEngine/ConstraintTypes.h"

// Shape Elements - UE 5.6.1 Complete
#include "PhysicsEngine/BoxElem.h"
#include "PhysicsEngine/SphereElem.h"
#include "PhysicsEngine/SphylElem.h"
#include "PhysicsEngine/ConvexElem.h"
#include "PhysicsEngine/TaperedCapsuleElem.h"

// Editor APIs - UE 5.6.1 Enhanced
#include "EditorAssetLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"

// Utilities
#include "HAL/PlatformFilemanager.h"
#include "Misc/Paths.h"
#include "UObject/Package.h"
#include "Engine/Engine.h"
#include "Engine/World.h"

/**
 * Handler class for Advanced Multilayer Collision System
 * 
 * Implements PRODUCTION READY collision system for Auracron's 3-layer MOBA
 * with custom collision profiles per layer, intelligent collision detection,
 * and advanced Chaos Physics integration.
 * 
 * Features:
 * - Layer-specific collision profiles (RadianteProfile, ZephyrProfile, UmbralProfile)
 * - Intelligent multilayer collision detection with cross-layer interactions
 * - Advanced Chaos Physics settings per layer with performance optimization
 * - Custom collision handlers for layer-specific collision responses
 * - Dynamic collision profile switching based on layer transitions
 * - Performance-optimized collision queries and spatial partitioning
 * - Integration with physics constraints and joint systems
 * - Advanced shape collision with custom geometry per layer
 * 
 * All implementations are PRODUCTION READY with:
 * - Thread safety (Game Thread execution)
 * - Robust parameter validation
 * - Mandatory disk saving
 * - Detailed logging
 * - Modern UE 5.6.1 APIs only
 */
class UNREALMCP_API FUnrealMCPCollisionCommands
{
public:
    FUnrealMCPCollisionCommands();

    /**
     * Main command handler dispatcher
     * @param CommandType The specific command to execute
     * @param Params JSON parameters for the command
     * @return JSON response with execution results
     */
    TSharedPtr<FJsonObject> HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params);

private:
    // ========================================
    // COLLISION PROFILE COMMANDS
    // ========================================

    /**
     * Creates layer-specific collision profiles with advanced Chaos Physics integration
     * 
     * Parameters:
     * - profile_system_name (string): Name of the collision profile system
     * - layer_profiles (array): Collision profiles per layer with detailed settings
     * - collision_channels (array): Custom collision channels for multilayer detection
     * - response_matrices (object): Collision response matrices per layer
     * - chaos_settings (object): Advanced Chaos Physics settings per layer
     * 
     * Implementation:
     * - Creates custom FCollisionProfileName entries per layer
     * - Configures advanced collision response matrices
     * - Sets up Chaos Physics solver settings per layer
     * - Implements performance-optimized collision detection
     * 
     * @param Params JSON parameters
     * @return JSON response with collision profile system details
     */
    TSharedPtr<FJsonObject> HandleCreateLayerCollisionProfiles(const TSharedPtr<FJsonObject>& Params);

    /**
     * Sets up intelligent multilayer collision detection system
     * 
     * Parameters:
     * - detection_system_name (string): Name of the detection system
     * - cross_layer_rules (object): Rules for cross-layer collision detection
     * - detection_ranges (object): Detection ranges per layer and interaction type
     * - filtering_settings (object): Advanced collision filtering per layer
     * - optimization_settings (object): Performance optimization configurations
     * 
     * Implementation:
     * - Creates custom collision detection algorithms
     * - Implements cross-layer collision filtering
     * - Sets up spatial partitioning for performance
     * - Configures advanced collision queries
     * 
     * @param Params JSON parameters
     * @return JSON response with intelligent detection system results
     */
    TSharedPtr<FJsonObject> HandleCreateIntelligentCollisionDetection(const TSharedPtr<FJsonObject>& Params);

    // ========================================
    // CHAOS PHYSICS COMMANDS
    // ========================================

    /**
     * Configures advanced Chaos Physics settings per layer
     * 
     * Parameters:
     * - chaos_system_name (string): Name of the Chaos Physics system
     * - solver_settings (object): Solver configurations per layer
     * - material_properties (object): Physics materials per layer
     * - constraint_settings (object): Physics constraints per layer
     * - performance_profiles (object): Performance optimization per layer
     * 
     * Implementation:
     * - Configures Chaos solver parameters per layer
     * - Sets up advanced physics materials
     * - Implements constraint systems per layer
     * - Optimizes physics performance per layer
     * 
     * @param Params JSON parameters
     * @return JSON response with Chaos Physics configuration results
     */
    TSharedPtr<FJsonObject> HandleConfigureChaosPhysicsPerLayer(const TSharedPtr<FJsonObject>& Params);

    /**
     * Creates custom collision handlers for layer-specific responses
     * 
     * Parameters:
     * - handler_system_name (string): Name of the collision handler system
     * - layer_handlers (array): Custom handlers per layer
     * - response_behaviors (object): Collision response behaviors per layer
     * - event_systems (object): Collision event handling per layer
     * - damage_systems (object): Damage calculation per layer collision
     * 
     * Implementation:
     * - Creates custom UPhysicsCollisionHandler subclasses
     * - Implements layer-specific collision responses
     * - Sets up collision event systems
     * - Configures damage calculation per layer
     * 
     * @param Params JSON parameters
     * @return JSON response with collision handler system results
     */
    TSharedPtr<FJsonObject> HandleCreateCustomCollisionHandlers(const TSharedPtr<FJsonObject>& Params);

    // ========================================
    // ADVANCED SHAPE COLLISION COMMANDS
    // ========================================

    /**
     * Sets up advanced shape collision with custom geometry per layer
     * 
     * Parameters:
     * - shape_system_name (string): Name of the shape collision system
     * - layer_geometries (array): Custom geometries per layer
     * - shape_types (array): Advanced shape types (convex, trimesh, levelset)
     * - collision_complexity (object): Collision complexity settings per layer
     * - mesh_optimization (object): Collision mesh optimization per layer
     * 
     * Implementation:
     * - Creates advanced collision shapes per layer
     * - Implements custom geometry collision
     * - Sets up collision mesh optimization
     * - Configures shape-specific collision responses
     * 
     * @param Params JSON parameters
     * @return JSON response with advanced shape collision results
     */
    TSharedPtr<FJsonObject> HandleCreateAdvancedShapeCollision(const TSharedPtr<FJsonObject>& Params);

    // ========================================
    // PERFORMANCE OPTIMIZATION COMMANDS
    // ========================================

    /**
     * Optimizes collision system performance for multilayer scenarios
     * 
     * Parameters:
     * - optimization_system_name (string): Name of the optimization system
     * - spatial_partitioning (object): Spatial partitioning settings per layer
     * - culling_settings (object): Collision culling configurations
     * - query_optimization (object): Collision query optimization settings
     * - threading_settings (object): Multi-threading configurations
     * 
     * Implementation:
     * - Sets up spatial partitioning per layer
     * - Implements collision culling systems
     * - Optimizes collision queries
     * - Configures multi-threading for collision
     * 
     * @param Params JSON parameters
     * @return JSON response with performance optimization results
     */
    TSharedPtr<FJsonObject> HandleOptimizeCollisionPerformance(const TSharedPtr<FJsonObject>& Params);

    // ========================================
    // UTILITY METHODS
    // ========================================

    /**
     * Validates required parameters for commands
     * @param Params Input parameters
     * @param RequiredFields Array of required field names
     * @param OutError Error message if validation fails
     * @return true if all required fields are present
     */
    bool ValidateRequiredParams(const TSharedPtr<FJsonObject>& Params, 
                               const TArray<FString>& RequiredFields, 
                               FString& OutError);

    /**
     * Ensures command execution on Game Thread
     * @param Command Lambda to execute on Game Thread
     */
    void ExecuteOnGameThread(TFunction<void()> Command);

    /**
     * Creates standardized error response
     * @param ErrorMessage The error message
     * @return JSON error response
     */
    TSharedPtr<FJsonObject> CreateErrorResponse(const FString& ErrorMessage);

    /**
     * Creates standardized success response
     * @param CommandName The executed command name
     * @param ResultData Additional result data
     * @return JSON success response
     */
    TSharedPtr<FJsonObject> CreateSuccessResponse(const FString& CommandName, 
                                                 const TSharedPtr<FJsonObject>& ResultData);

    // ========================================
    // INTERNAL COLLISION LOGIC
    // ========================================

    /**
     * Creates custom collision profile for specific layer
     * @param LayerName Name of the layer
     * @param LayerIndex Index of the layer
     * @param ProfileSettings Profile configuration settings
     * @return Created collision profile name
     */
    FName CreateLayerCollisionProfile(const FString& LayerName, 
                                     int32 LayerIndex,
                                     const TSharedPtr<FJsonObject>& ProfileSettings);

    /**
     * Configures Chaos Physics solver for specific layer
     * @param LayerIndex Layer to configure
     * @param SolverSettings Solver configuration
     * @return Success status
     */
    bool ConfigureChaosLayerSolver(int32 LayerIndex, 
                                  const TSharedPtr<FJsonObject>& SolverSettings);

    /**
     * Sets up collision detection between two layers
     * @param SourceLayer Source layer index
     * @param TargetLayer Target layer index
     * @param DetectionRules Detection rules configuration
     * @return Success status
     */
    bool SetupCrossLayerCollisionDetection(int32 SourceLayer, 
                                          int32 TargetLayer,
                                          const TSharedPtr<FJsonObject>& DetectionRules);

private:
    // Collision profiles per layer (using FCollisionProfileName for modern API)
    TMap<int32, FCollisionProfileName> LayerCollisionProfiles;

    // Custom collision handlers per layer
    TMap<int32, TWeakObjectPtr<UPhysicsCollisionHandler>> LayerCollisionHandlers;

    // Chaos Physics settings per layer
    TMap<int32, TSharedPtr<FJsonObject>> LayerChaosSettings;

    // Cross-layer collision detection rules
    TMap<TPair<int32, int32>, TSharedPtr<FJsonObject>> CrossLayerDetectionRules;

    // New robust implementation variables
    TMap<FString, ECollisionChannel> LayerCollisionChannels;
    TMap<int32, TSharedPtr<FJsonObject>> LayerSolverConfigurations;
    TMap<int32, TSharedPtr<FJsonObject>> LayerCollisionHandlerConfigs;

    // Modern UE 5.6.1 collision delegate storage using TMulticastDelegate
    DECLARE_MULTICAST_DELEGATE_FiveParams(FLayerCollisionDelegate, UPrimitiveComponent*, AActor*, UPrimitiveComponent*, FVector, const FHitResult&);
    TMap<int32, FLayerCollisionDelegate> LayerCollisionDelegates;
    
    // Performance optimization settings
    struct FCollisionPerformanceSettings
    {
        bool bUseSpatialPartitioning = true;
        bool bUseCollisionCulling = true;
        bool bOptimizeQueries = true;
        bool bUseMultiThreading = true;
        int32 MaxCollisionChecks = 1000;
        float CullingDistance = 5000.0f;
    } PerformanceSettings;
};
