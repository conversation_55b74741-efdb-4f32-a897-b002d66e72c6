<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <None Include="..\..\AURACRON.uproject" />
    <Filter Include="Source">
      <UniqueIdentifier>{F31BBDD1-B3E8-3BCC-9652-680E16935819}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Source\AURACRON.Target.cs">
      <Filter>Source</Filter>
    </None>
    <None Include="..\..\Source\AuracronEditor.Target.cs">
      <Filter>Source</Filter>
    </None>
    <None Include="..\..\.vsconfig" />
    <None Include="..\..\ANALISE.md" />
    <None Include="..\..\log.md" />
    <None Include="..\..\ManualPCG.md" />
    <None Include="..\..\MANUAL_IMPLEMENTACAO_UnrealMCP.md" />
    <None Include="..\..\mapsdetailed.md" />
    <None Include="..\..\README_WorldPartitionFix.md" />
    <None Include="..\..\unreal_mcp.log" />
    <None Include="..\..\WorldPartitionFix_AutoApply.ps1" />
    <Filter Include="Config">
      <UniqueIdentifier>{FA535FFB-25E1-3D20-B416-52F9BE21E06E}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Config\DefaultEditor.ini">
      <Filter>Config</Filter>
    </None>
    <None Include="..\..\Config\DefaultEngine.ini">
      <Filter>Config</Filter>
    </None>
    <None Include="..\..\Config\DefaultGame.ini">
      <Filter>Config</Filter>
    </None>
    <None Include="..\..\Config\DefaultInput.ini">
      <Filter>Config</Filter>
    </None>
    <Filter Include="Plugins">
      <UniqueIdentifier>{BB38096A-B391-30DC-A0D4-4F3EA6B44507}</UniqueIdentifier>
    </Filter>
    <Filter Include="Plugins\UnrealMCP">
      <UniqueIdentifier>{D741E9D7-2443-3229-946E-239D2700AD33}</UniqueIdentifier>
    </Filter>
    <Filter Include="Plugins\UnrealMCP\Source">
      <UniqueIdentifier>{AA5862E2-2B5E-3C54-B14F-C9ADE0CB000C}</UniqueIdentifier>
    </Filter>
    <Filter Include="Plugins\UnrealMCP\Source\UnrealMCP">
      <UniqueIdentifier>{A8C084DA-1928-35F8-8A07-EB43CE50FCDC}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\UnrealMCP.Build.cs">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP</Filter>
    </None>
    <Filter Include="Plugins\UnrealMCP\Source\UnrealMCP\Private">
      <UniqueIdentifier>{D919B08C-445D-3FCD-9DFE-48A112DE3341}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\MCPServerRunnable.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\UnrealMCPBridge.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\UnrealMCPModule.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private</Filter>
    </ClCompile>
    <Filter Include="Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands">
      <UniqueIdentifier>{511278AC-FC06-333C-84C2-B5EDF8469817}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPAnalyticsCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPArchitectureCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPBalanceCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPBlueprintCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPBlueprintNodeCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPCollisionAdvancedCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPCollisionCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPCommonUtils.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPEditorCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPLandscapeCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPMapCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPMaterialCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPMOBACommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPPathfindingCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPProceduralMeshCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPProjectCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPUMGCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPVisionCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands\UnrealMCPVisualEffectsCommands.cpp">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Private\Commands</Filter>
    </ClCompile>
    <Filter Include="Plugins\UnrealMCP\Source\UnrealMCP\Public">
      <UniqueIdentifier>{AFF61063-E844-32A0-A18E-7FE47B9AEB77}</UniqueIdentifier>
    </Filter>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\MCPServerRunnable.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\UnrealMCPBridge.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\UnrealMCPModule.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public</Filter>
    </ClInclude>
    <Filter Include="Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands">
      <UniqueIdentifier>{7460A6C2-A1BB-3667-ACB1-35E16D0E8F89}</UniqueIdentifier>
    </Filter>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPAnalyticsCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPArchitectureCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPBalanceCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPBlueprintCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPBlueprintNodeCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPCollisionAdvancedCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPCollisionCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPCommonUtils.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPEditorCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPLandscapeCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPMapCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPMaterialCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPMOBACommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPPathfindingCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPProceduralMeshCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPProjectCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPUMGCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPVisionCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands\UnrealMCPVisualEffectsCommands.h">
      <Filter>Plugins\UnrealMCP\Source\UnrealMCP\Public\Commands</Filter>
    </ClInclude>
    <None Include="..\..\Plugins\UnrealMCP\UnrealMCP.uplugin">
      <Filter>Plugins\UnrealMCP</Filter>
    </None>
    <Filter Include="Source\AURACRON">
      <UniqueIdentifier>{38706F87-0F5F-30F3-ACA7-4436234CDD96}</UniqueIdentifier>
    </Filter>
    <None Include="..\..\Source\AURACRON\AURACRON.Build.cs">
      <Filter>Source\AURACRON</Filter>
    </None>
    <ClCompile Include="..\..\Source\AURACRON\AURACRON.cpp">
      <Filter>Source\AURACRON</Filter>
    </ClCompile>
    <ClInclude Include="..\..\Source\AURACRON\AURACRON.h">
      <Filter>Source\AURACRON</Filter>
    </ClInclude>
    <Filter Include="Source\AURACRON\Private">
      <UniqueIdentifier>{E7F264DC-6C17-3666-B3EC-EA0F417E010B}</UniqueIdentifier>
    </Filter>
    <ClCompile Include="..\..\Source\AURACRON\Private\WorldPartitionFix.cpp">
      <Filter>Source\AURACRON\Private</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\AURACRON\Private\WorldPartitionFixSubsystem.cpp">
      <Filter>Source\AURACRON\Private</Filter>
    </ClCompile>
    <Filter Include="Source\AURACRON\Public">
      <UniqueIdentifier>{30D56F11-4DA0-389E-A273-B3AD9FE16C61}</UniqueIdentifier>
    </Filter>
    <ClInclude Include="..\..\Source\AURACRON\Public\WorldPartitionFix.h">
      <Filter>Source\AURACRON\Public</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\AURACRON\Public\WorldPartitionFixSubsystem.h">
      <Filter>Source\AURACRON\Public</Filter>
    </ClInclude>
  </ItemGroup>
</Project>
