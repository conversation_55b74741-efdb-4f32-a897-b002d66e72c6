# AURACRON - Detailed Map Design Document

## 1. Overview
This document contains comprehensive information for creating the revolutionary three-layered map system for AURACRON, based on research from League of Legends and Dota 2, adapted for our unique multi-dimensional concept.

## 2. Terrain Textures and Materials

### 2.1 Grass Materials
- **Multi-layer texture blending**: Use multiple texture layers blended with shaders and alpha blending
- **Displacement maps**: Apply alpha blending on displacement maps for visual variety
- **Tileable textures**: Create seamless grass textures using Maya, ZBrush, and Substance Designer
- **Variation system**: Implement texture variation to avoid repetition across large areas
- **Layer-specific grass types**:
  - Planície Radiante: Luminous grass with golden highlights
  - Firmamento Zephyr: Ethereal grass that sways with wind currents
  - Abismo Umbral: Dark, shadowy grass with purple undertones

### 2.2 Stone and Rock Materials
- **Modular rock formations**: Create prefab rock assets for consistent placement
- **Destructible elements**: Some rocks can be destroyed for strategic gameplay
- **Texture variety**: Multiple stone textures for different areas (ancient, weathered, crystalline)
- **Normal mapping**: High-detail normal maps for realistic surface detail
- **Layer-specific materials**:
  - Planície: Sunlit sandstone and marble
  - Firmamento: Floating crystal formations
  - Abismo: Dark obsidian and shadow-touched stone

### 2.3 Water and River Systems
- **Shader implementation**: Use Source engine-style water shaders with reflections and refractions
- **Flow mapping**: Implement water flow direction and speed
- **Two-material system**: Separate materials for above and below water surface
- **Performance options**: Expensive (real-time reflection) and cheap (envmap) versions
- **Interactive elements**: Water affects certain abilities and spells
- **Cosmetic variations**: Different water colors and effects per layer

## 3. Environmental Assets

### 3.1 Vegetation System
- **Destructible trees**: Trees block vision and movement, respawn after 3 minutes
- **Interactive plants**: Jungle plants with specific effects (similar to LoL's system)
  - Blast Cone equivalent: Launch players between layers
  - Honeyfruit equivalent: Layer-specific healing plants
  - Scryer's Bloom equivalent: Vision plants for multi-layer scouting
- **Tree variety**: Different tree types for each layer
- **Foliage density**: Optimized LOD system for performance

### 3.2 Structural Elements
- **Defensive structures**: Towers, inhibitors, and base fortifications
- **Permanent walls**: Medieval castle-style fortifications
- **Modular prefabs**: Reusable building components
- **Cosmetic destruction**: Visual damage that doesn't affect functionality
- **Portal structures**: Unique architecture for vertical transition points

## 4. Lighting and Visual Effects

### 4.1 Day/Night Cycle
- **Dynamic lighting**: Implement day/night cycle affecting vision range
- **Layer-specific lighting**:
  - Planície: Bright daylight with golden hour effects
  - Firmamento: Ethereal twilight with floating light sources
  - Abismo: Perpetual darkness with mystical illumination
- **Vision mechanics**: Different heroes have varying night vision capabilities
- **Audio-visual cues**: Clear indicators for time transitions

### 4.2 Rendering Technology
- **Specular bloom**: Enhanced lighting effects
- **Additive light pass**: Realistic reflections and highlights
- **World lighting**: Global illumination system
- **Ambient occlusion**: Realistic shadow depth
- **Performance scaling**: Multiple quality settings for different hardware

### 4.3 Weather Systems
- **Cosmetic weather effects**:
  - Rain: Atmospheric particles with sound effects
  - Snow: Seasonal effects with movement impact
  - Fog: Reduced vision mechanics
  - Ash: Volcanic effects for dramatic atmosphere
  - Moonbeam: Mystical lighting effects
- **Gameplay-affecting weather** (optional):
  - Heavy rain: Increased spell damage/attack speed
  - Snowfall: Decreased movement speed
  - Fog: Reduced vision range
  - Acid rain: Armor reduction effects

## 5. Multi-Layer Specific Elements

### 5.1 Vertical Connectors
- **Primary Portals**: Large, ornate structures for main transitions
  - Dimensions: 8m diameter, 12m height
  - Visual effects: Swirling energy, particle systems
  - Audio cues: Distinctive portal sounds
- **Mystic Elevators**: Mechanical/magical lift systems
  - Platform size: 6m x 6m
  - Travel time: 3-5 seconds between layers
  - Visual indicators: Clear destination markers
- **Dimensional Bridges**: Temporary or permanent connections
  - Width: 4m for strategic movement
  - Length: Variable based on layer distance
  - Stability mechanics: Some bridges may be destructible

### 5.2 Layer-Specific Objectives
- **Planície Radiante**:
  - Guardião da Aurora: Central boss with light-based abilities
  - Cristais de Luz: Resource nodes providing team buffs
  - Torres de Cristal: Enhanced defensive structures
- **Firmamento Zephyr**:
  - Senhor dos Ventos: Aerial boss with wind manipulation
  - Esferas de Tempestade: Storm orbs providing movement buffs
  - Floating platforms: Dynamic terrain elements
- **Abismo Umbral**:
  - Arqui-Sombra: Shadow lord with stealth abilities
  - Fragmentos Umbrais: Dark crystals providing stealth buffs
  - Shadow portals: Quick travel within the layer

## 6. Technical Implementation

### 6.1 Unreal Engine 5 Features
- **World Composition**: Seamless world streaming
- **Level Streaming**: Dynamic loading of layer sections
- **Nanite**: Virtualized geometry for detailed assets
- **Lumen**: Dynamic global illumination
- **World Partition**: Optimized world management

### 6.2 Performance Optimization
- **LOD System**: Multiple detail levels for all assets
- **Occlusion Culling**: Hide non-visible geometry
- **Instanced Rendering**: Efficient rendering of repeated elements
- **Texture Streaming**: Dynamic texture loading
- **Audio Streaming**: Efficient sound management

### 6.3 Collision System
- **Multi-layer collision profiles**: Separate collision for each layer
- **Vertical transition zones**: Special collision for portal areas
- **Performance optimization**: Efficient collision detection algorithms
- **Projectile handling**: Multi-layer projectile physics

## 7. Pathfinding and Navigation

### 7.1 Multi-Layer A* Algorithm
- **Horizontal movement costs**:
  - Normal terrain: 1.0
  - Jungle: 1.2
  - River: 0.9
  - Rough terrain: 1.5
- **Vertical movement costs**:
  - Portal usage: 2.0
  - Elevator: 3.0
  - Bridge: 1.5
- **Layer transition penalties**: Additional cost for changing layers

### 7.2 Navigation Aids
- **Visual indicators**: Clear markers for available paths
- **Minimap integration**: Multi-layer minimap system
- **Mobile adaptations**: Touch-friendly navigation controls
- **Assisted navigation**: Optional pathfinding assistance

## 8. Vision and Fog of War

### 8.1 Three-Dimensional Vision System
- **Standard vision range**: Horizontal sight within same layer
- **Vertical vision**: Limited sight between layers
- **Multi-layered Fog of War**: Separate fog system for each layer
- **Stealth mechanics**: Layer-specific invisibility systems

### 8.2 Vision Mechanics
- **Ward placement**: Strategic vision control across layers
- **High ground advantage**: Enhanced vision from upper layers
- **Vision blocking**: Terrain and structures block sight lines
- **Dynamic vision**: Vision changes based on layer positioning

## 9. Audio Design

### 9.1 Ambient Sound System
- **Layer-specific ambient tracks**: Each layer has unique background music and ambient sounds
- **Dynamic music system**: Music intensity changes based on game state (peaceful farming vs team fights)
- **Positional audio**: 3D audio for better spatial awareness using Source engine ambient_generic entities
- **Environmental sounds**: Water flowing, wind in trees, underground echoes
- **Soundscape system**: Using env_soundscape for environmental audio zones
- **Audio mixing**: Dynamic audio mixing similar to Dota 2's acclaimed system

### 9.2 Interactive Audio
- **Footstep variations**: Different sounds for different terrain types
- **Ability sound mixing**: Spells sound different in different layers
- **Combat audio scaling**: Audio intensity scales with fight magnitude
- **Announcer system**: Dynamic announcer that responds to multi-layer events
- **Audio feedback**: Clear audio cues for vertical transitions and layer-specific events

### 9.3 Technical Audio Implementation
- **Sound radius control**: Proper audio distance falloff for each layer
- **Audio occlusion**: Sounds from other layers are muffled appropriately
- **Performance optimization**: Efficient sound streaming and memory management
- **Customizable audio**: Players can adjust layer-specific audio levels

## 10. Asset Creation Guidelines

### 10.1 Modeling Standards
- **Polygon budgets**: Optimized poly counts for different asset types
- **UV mapping**: Efficient texture coordinate layouts
- **Normal maps**: High-to-low poly baking workflows
- **Material assignments**: Consistent material naming conventions

### 10.2 Texture Standards
- **Resolution guidelines**: 1024x1024 for most textures, 2048x2048 for hero assets
- **Format specifications**: DXT compression for optimization
- **Mipmap generation**: Automatic mipmap creation for distance rendering
- **Texture atlasing**: Combine small textures for efficiency

### 10.3 Animation Requirements
- **Environmental animations**: Swaying trees, flowing water, floating platforms
- **Interactive animations**: Portal activations, elevator movements
- **Particle systems**: Magical effects, weather particles, combat effects
- **Performance considerations**: LOD for animations and particles

## 11. Quality Assurance

### 11.1 Performance Targets
- **Frame rate**: Maintain 60 FPS on recommended hardware
- **Memory usage**: Optimize for 8GB RAM systems
- **Loading times**: Layer transitions under 2 seconds
- **Network performance**: Minimize bandwidth for multi-layer updates

### 11.2 Visual Quality Standards
- **Consistency**: Maintain art style across all layers
- **Readability**: Ensure gameplay elements are clearly visible
- **Accessibility**: Support for colorblind players
- **Scalability**: Multiple quality settings for different hardware

## 12. Implementation Timeline

### Phase 1: Foundation (Months 1-3)
- Basic terrain creation
- Core texture development
- Initial lighting setup
- Basic collision implementation

### Phase 2: Layer Development (Months 4-8)
- Complete Planície Radiante layer
- Develop Firmamento Zephyr layer
- Create Abismo Umbral layer
- Implement vertical connectors

### Phase 3: Polish and Optimization (Months 9-12)
- Performance optimization
- Visual effects implementation
- Audio integration
- Quality assurance testing

### Phase 4: Final Integration (Months 13-15)
- Gameplay balancing
- Final bug fixes
- Performance tuning
- Launch preparation

This document serves as the comprehensive guide for creating all visual, audio, and technical elements needed for AURACRON's revolutionary three-layered map system.