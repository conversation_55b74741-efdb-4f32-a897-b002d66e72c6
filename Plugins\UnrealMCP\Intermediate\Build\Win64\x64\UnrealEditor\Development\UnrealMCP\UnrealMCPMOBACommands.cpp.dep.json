{"Version": "1.2", "Data": {"Source": "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\private\\commands\\unrealmcpmobacommands.cpp", "ProvidedModule": "", "PCH": "c:\\game\\auracron\\intermediate\\build\\win64\\x64\\auracroneditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\game\\auracron\\plugins\\unrealmcp\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\unrealmcp\\definitions.unrealmcp.h", "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpmobacommands.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\json\\public\\json.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monolithicheaderboilerplate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\core.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monolithicheaderboilerplate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformnamedpipe.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\platformnamedpipe.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\windows\\windowsplatformnamedpipe.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformmutex.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\platformincludes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monolithicheaderboilerplate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\profilingdebugging\\scopeddebuginfo.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\profilingdebugging\\externalprofiler.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\stringutility.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\nameasstringproxyarchive.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\mruarray.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\arraybuilder.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\singlethreadevent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\threadmanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\staticbitarray.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\mapbuilder.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\threadingbase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\internationalization\\textlocalizationmanagerglobals.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\internationalization\\culture.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\logging\\logsuppressioninterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\hal\\outputdevices.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\logging\\logscopedverbosityoverride.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicenull.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicememory.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicefile.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicedebug.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicearchivewrapper.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdeviceansierror.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\timeguard.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\largememorywriter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\largememorydata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\largememoryreader.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\bufferarchive.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\arrayreader.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\arraywriter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\bufferwriter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\wildcardstring.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\circularqueue.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdevicehelper.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\callbackdevice.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\localtimestampdirectoryvisitor.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\blueprintsobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\buildobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\coreobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\frameworkobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\mobileobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\networkingobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\onlineobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\platformobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\sequencerobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\uobject\\vrobjectversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\outputdeviceconsole.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\monitoredprocess.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\gamemode.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\gamemodebase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\serverstatreplicator.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\serverstatreplicator.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gamemodebase.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gamemode.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\gamestate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\gamestatebase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gamestatebase.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gamestate.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\playerstate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\playerstate.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\aicontroller.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\aitypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\navigationsystem\\public\\navigationsystemtypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navagentselector.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navagentselector.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aitypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\perception\\aiperceptionlistenerinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aiperceptionlistenerinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\genericteamagentinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\genericteamagentinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\visuallogger\\visualloggerdebugsnapshotinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\visualloggerdebugsnapshotinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aicontroller.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\blueprint\\aiblueprinthelperlibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aiblueprinthelperlibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\behaviortree\\behaviortree.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\behaviortree\\btcompositenode.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\behaviortree\\btnode.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\behaviortree\\behaviortreetypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\behaviortree\\blackboard\\blackboardkey.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\behaviortreetypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\behaviortree\\behaviortreecomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\gameplaytags\\classes\\gameplaytagcontainer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\comparisonutility.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytags\\uht\\gameplaytagcontainer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\braincomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\airesourceinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\airesourceinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\braincomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\behaviortreecomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\behaviortree\\blackboardassetprovider.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\blackboardassetprovider.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\tasks\\aitask.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aitask.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\btnode.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\btcompositenode.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\behaviortree.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\behaviortree\\blackboardcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\behaviortree\\blackboard\\blackboardkeytype.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\behaviortree\\blackboard\\blackboardkeyenums.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\blackboardkeyenums.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\blackboardkeytype.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\aisystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\aisystembase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\aisystembase.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\aisystem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\aimodule\\classes\\behaviortree\\blackboarddata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\blackboarddata.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\aimodule\\uht\\blackboardcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\capsulecomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\shapecomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\shapecomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\capsulecomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\spherecomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\spherecomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\boxcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\boxcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\audiocomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\audio\\soundparametercontrollerinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\soundparametercontrollerinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\audioextensions\\public\\iaudioparametertransmitter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\audiomixer\\public\\quartz\\audiomixerquantizedcommands.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\audiomixer\\public\\quartz\\audiomixerclock.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\audiomixer\\public\\quartz\\quartzmetronome.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\quartzsubscription.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\quartzsubscriptiontoken.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\quartzinterfaces.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\audiocomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystatics.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetsystemlibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\propertyaccessutil.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\kismetsystemlibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\dialoguetypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\dialoguetypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystaticstypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gameplaystaticstypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gameplaystatics.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetmathlibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\kismetmathlibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetmathlibrary.inl", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\editor\\editorscriptingutilities\\source\\editorscriptingutilities\\public\\editorassetlibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\editor\\editorscriptingutilities\\intermediate\\build\\win64\\unrealeditor\\inc\\editorscriptingutilities\\uht\\editorassetlibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\assetregistry\\public\\assetregistry\\assetregistrymodule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\assetregistry\\public\\assetregistry\\iassetregistry.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\misc\\assetregistryinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\assetregistry\\uht\\iassetregistry.generated.h", "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpcommonutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\leveleditor\\public\\leveleditor.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\leveleditor\\public\\ileveleditor.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\elements\\framework\\engineelementslibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\engineelementslibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\leveleditor\\public\\viewporttypedefinition.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\leveleditor\\public\\leveleditoroutlinersettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\toolwidgets\\public\\filters\\genericfilter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\toolwidgets\\public\\filters\\filterbase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\public\\subsystems\\editorassetsubsystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealed\\uht\\editorassetsubsystem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\assettools\\public\\assettoolsmodule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\assettools\\public\\iassettools.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\assettools\\public\\assettypecategories.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\assettools\\public\\iassettypeactions.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\developer\\merge\\public\\merge.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\classes\\thumbnailrendering\\thumbnailmanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\classes\\thumbnailrendering\\thumbnailrenderer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealed\\uht\\thumbnailrenderer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealed\\uht\\thumbnailmanager.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\public\\automatedassetimportdata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealed\\uht\\automatedassetimportdata.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\assettools\\uht\\iassettools.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\savepackage.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\packagewriter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\io\\iodispatcher.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\io\\iobuffer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\io\\iocontainerid.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\ienginecrypto.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\objectsaveoverride.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\staticmeshactor.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\staticmeshactor.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprinteditorlibrary\\public\\blueprinteditorlibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprinteditorlibrary\\uht\\blueprinteditorlibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\particles\\particlesystemcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\particles\\particlesystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\particles\\particleperfstats.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\particlesystem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\particles\\emitter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\emitter.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\statestream\\particlesystemstatestreamhandle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\particlesystemstatestreamhandle.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\particlesystemcomponent.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}