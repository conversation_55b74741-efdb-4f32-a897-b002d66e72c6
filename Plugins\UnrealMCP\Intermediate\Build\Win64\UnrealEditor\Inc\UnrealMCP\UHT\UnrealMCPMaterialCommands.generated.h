// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Commands/UnrealMCPMaterialCommands.h"

#ifdef UNREALMCP_UnrealMCPMaterialCommands_generated_h
#error "UnrealMCPMaterialCommands.generated.h already included, missing '#pragma once' in UnrealMCPMaterialCommands.h"
#endif
#define UNREALMCP_UnrealMCPMaterialCommands_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FAuracronLayerMaterialConfig **************************************
#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPMaterialCommands_h_58_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAuracronLayerMaterialConfig_Statics; \
	static class UScriptStruct* StaticStruct();


struct FAuracronLayerMaterialConfig;
// ********** End ScriptStruct FAuracronLayerMaterialConfig ****************************************

// ********** Begin ScriptStruct FDynamicMaterialParams ********************************************
#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPMaterialCommands_h_147_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FDynamicMaterialParams_Statics; \
	static class UScriptStruct* StaticStruct();


struct FDynamicMaterialParams;
// ********** End ScriptStruct FDynamicMaterialParams **********************************************

// ********** Begin Class UUnrealMCPMaterialCommands ***********************************************
UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPMaterialCommands_NoRegister();

#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPMaterialCommands_h_177_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUUnrealMCPMaterialCommands(); \
	friend struct Z_Construct_UClass_UUnrealMCPMaterialCommands_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend UNREALMCP_API UClass* Z_Construct_UClass_UUnrealMCPMaterialCommands_NoRegister(); \
public: \
	DECLARE_CLASS2(UUnrealMCPMaterialCommands, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/UnrealMCP"), Z_Construct_UClass_UUnrealMCPMaterialCommands_NoRegister) \
	DECLARE_SERIALIZER(UUnrealMCPMaterialCommands)


#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPMaterialCommands_h_177_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UUnrealMCPMaterialCommands(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	/** Deleted move- and copy-constructors, should never be used */ \
	UUnrealMCPMaterialCommands(UUnrealMCPMaterialCommands&&) = delete; \
	UUnrealMCPMaterialCommands(const UUnrealMCPMaterialCommands&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UUnrealMCPMaterialCommands); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UUnrealMCPMaterialCommands); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UUnrealMCPMaterialCommands) \
	NO_API virtual ~UUnrealMCPMaterialCommands();


#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPMaterialCommands_h_174_PROLOG
#define FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPMaterialCommands_h_177_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPMaterialCommands_h_177_INCLASS_NO_PURE_DECLS \
	FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPMaterialCommands_h_177_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UUnrealMCPMaterialCommands;

// ********** End Class UUnrealMCPMaterialCommands *************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Game_AURACRON_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPMaterialCommands_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
