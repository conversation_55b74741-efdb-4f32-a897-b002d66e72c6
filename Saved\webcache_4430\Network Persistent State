{"net": {"http_server_properties": {"servers": [{"alternative_service": [{"advertised_alpns": ["h3-29"], "expiration": "13403493245511373", "port": 443, "protocol_str": "quic"}], "isolation": [], "server": "https://www.google-analytics.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3-29"], "expiration": "13403493245589333", "port": 443, "protocol_str": "quic"}], "isolation": [], "server": "https://www.googletagmanager.com", "supports_spdy": true}, {"isolation": [], "server": "https://cdn.quixel.com", "supports_spdy": true}, {"isolation": [], "server": "https://d3uwib8iif8w1p.cloudfront.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3-29"], "expiration": "13403493244275383", "port": 443, "protocol_str": "quic"}], "isolation": [], "network_stats": {"srtt": 12581}, "server": "https://fonts.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3-29"], "expiration": "13403493248162260", "port": 443, "protocol_str": "quic"}], "isolation": [], "network_stats": {"srtt": 10365}, "server": "https://fonts.gstatic.com", "supports_spdy": true}, {"isolation": [], "server": "https://ddinktqu5prvc.cloudfront.net", "supports_spdy": true}], "supports_quic": {"address": "2804:14c:c3:8033:39d5:b9b5:91c7:5892", "used_quic": true}, "version": 5}, "network_qualities": {"CAASABiAgICA+P////8B": "4G"}}}